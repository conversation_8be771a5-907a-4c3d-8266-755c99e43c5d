using DSDK.Core;
using DSDK.Data;
using DSDK.UISystem;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class CurrencyOverlay : UIPanel
{

    [SerializeField] private Sprite[] heartSprites;
    [SerializeField] private Image heartIcon;

    [SerializeField] private TextMesh<PERSON><PERSON>UGUI coinText;
    [SerializeField] private TextMeshP<PERSON>UGUI heartCountText;
    [SerializeField] private TextMesh<PERSON><PERSON>UGUI heartCDText;

    [SerializeField] private RectTransform heartRect;
    [SerializeField] private RectTransform heartMenuPosition;
    [SerializeField] private RectTransform heartInGamePosition;

    [SerializeField] private float timeResetHeart;

    private const int HEART_REGENERATION_TIME = 900;
    private const int MAX_HEARTS = 5;
    void OnEnable()
    {
        this.AddEventListener(EventID.OnCoinChange, OnCoinChange);
        this.AddEventListener(EventID.OnHeartCountChange, OnHeartCountChange);

        if (GameData.Instance.CurrentHeart < MAX_HEARTS && GameData.Instance.NextHeartRegenerationTime == 0)
        {
            StartHeartRegeneration();
        }
    }
    void OnDisable()
    {
        this.RemoveEventListener(EventID.OnCoinChange, OnCoinChange);
        this.RemoveEventListener(EventID.OnHeartCountChange, OnHeartCountChange);
    }
    void Start()
    {
        OnCoinChange();
        OnHeartCountChange();
    }
    public void SetHeartPosition(bool isMenu)
    {
        heartRect.anchoredPosition = isMenu ? heartMenuPosition.anchoredPosition : heartInGamePosition.anchoredPosition;
    }
    private void OnCoinChange()
    {
        coinText.text = GameData.Instance.CurrentCoin.ToString();
    }
    private void OnHeartCountChange()
    {
        heartCountText.text = GameData.Instance.CurrentHeart.ToString();

        if (GameData.Instance.CurrentHeart < MAX_HEARTS && GameData.Instance.NextHeartRegenerationTime == 0)
        {
            StartHeartRegeneration();
        }
        else if (GameData.Instance.CurrentHeart >= MAX_HEARTS)
        {
            GameData.Instance.NextHeartRegenerationTime = 0;
        }
    }

    void Update()
    {

        if (GameData.Instance.FreeHeartRemainTime > 0)
        {
            if (heartIcon.sprite != heartSprites[1])
            {
                heartIcon.sprite = heartSprites[1];
            }
            heartCountText.text = "";
            heartCDText.text = "FULL";
            return;
        }

        if (heartIcon.sprite != heartSprites[0])
        {
            heartIcon.sprite = heartSprites[0];
        }

        if (GameData.Instance.CurrentHeart < MAX_HEARTS)
        {
            UpdateHeartRegeneration();
            GameData.Instance.remainingTime = GetHeartRegenerationRemainTime();

            if (GameData.Instance.remainingTime > 0)
            {
                heartCDText.text = Util.ConvertTime2((int)GameData.Instance.remainingTime);
            }
            else
            {
                heartCDText.text = "";
            }
        }
        else
        {
            heartCDText.text = "FULL";
        }
    }

    private void StartHeartRegeneration()
    {
        int currentTime = (int)(System.DateTime.UtcNow.Ticks / System.TimeSpan.TicksPerSecond);
        GameData.Instance.NextHeartRegenerationTime = currentTime + HEART_REGENERATION_TIME;
    }

    private float GetHeartRegenerationRemainTime()
    {
        if (GameData.Instance.CurrentHeart >= MAX_HEARTS || GameData.Instance.NextHeartRegenerationTime == 0)
            return 0f;

        int currentTime = (int)(System.DateTime.UtcNow.Ticks / System.TimeSpan.TicksPerSecond);
        return Mathf.Max(0f, GameData.Instance.NextHeartRegenerationTime - currentTime);
    }

    private void UpdateHeartRegeneration()
    {
        if (GameData.Instance.CurrentHeart >= MAX_HEARTS || GameData.Instance.NextHeartRegenerationTime == 0)
            return;

        int currentTime = (int)(System.DateTime.UtcNow.Ticks / System.TimeSpan.TicksPerSecond);

        while (currentTime >= GameData.Instance.NextHeartRegenerationTime && GameData.Instance.CurrentHeart < MAX_HEARTS)
        {
            GameData.Instance.CurrentHeart++;

            if (GameData.Instance.CurrentHeart < MAX_HEARTS)
            {
                GameData.Instance.NextHeartRegenerationTime += HEART_REGENERATION_TIME;
            }
            else
            {
                GameData.Instance.NextHeartRegenerationTime = 0;
            }

            OnHeartCountChange();
        }
    }
}
