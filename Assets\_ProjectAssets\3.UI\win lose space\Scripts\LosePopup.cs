using DSDK.Audio;
using DSDK.Data;
using DSDK.UISystem;
using DSDK.Vibration;
using Spine.Unity;
using UnityEngine;

public class LosePopup : UIPanel
{
    [SerializeField] private BaseBtn tryAgainBtn;
    [SerializeField] private BaseBtn homeBtn;
    [SerializeField] SkeletonGraphic skeletonGraphic;
    [SerializeField] SkeletonGraphic titleSpine;

    public override void Show()
    {
        base.Show();
        titleSpine.AnimationState.SetAnimation(0, "lose/lose_spawn", false);
        titleSpine.AnimationState.AddAnimation(0, "lose/lose_idle", true, 0f);
        AudioServer.I.Pause("Music");
        AudioServer.I.Shot(eAudio.SFX_Lose.ToString());
        VibrationManager.Instance.Vibrate(HapticType.Large, 250);
        skeletonGraphic.AnimationState.SetAnimation(0, Random.Range(0, 2) == 0 ? "lose1" : "lose2", true);
    }
    public override void Hide()
    {
        base.Hide();
        AudioServer.I.Resume("Music");
    }
    private void Start()
    {
        tryAgainBtn.OnClick(OnTryAgainBtnClick);
        homeBtn.OnClick(OnHomeBtnClick);
    }
    private void OnHomeBtnClick()
    {
        GameManager.Instance.RewardHeart(-1);
        GameManager.Instance.BackToHomeScreen();
        Hide();
    }
    private void OnTryAgainBtnClick()
    {
        GameManager.Instance.RewardHeart(-1);
        GameManager.Instance.BackToHomeScreen();
        Hide();
    }
}
