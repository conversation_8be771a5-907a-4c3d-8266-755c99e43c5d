using DSDK.Audio;
using DSDK.Data;
using DSDK.UISystem;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class OutOfSpacePopup : UIPanel
{
    [SerializeField] private BaseBtn giveUpBtn;
    [SerializeField] private BaseBtn _payReviveBtn;
    [SerializeField] private BaseBtn _closeBtn;
    [SerializeField] private TextMeshProUGUI _payCoinText;


    void Start()
    {
        _payCoinText.text = GameManager.Instance.GameConfigManager.RevivePrice.ToString();
        giveUpBtn.OnClick(OnGiveUpBtnClick);
        _payReviveBtn.OnClick(OnPayReviveBtnClick);
        _closeBtn.OnClick(OnGiveUpBtnClick);
    }

    public override void Show()
    {
        base.Show();
        AudioServer.I.Pause("Music");
        _payReviveBtn.CanInvokeAction = GameManager.Instance.GetCurrentCoin() >= GameManager.Instance.GameConfigManager.RevivePrice;
    }

    public override void Hide()
    {
        base.Hide();
        AudioServer.I.<PERSON>sume("Music");
    }

    private void OnPayReviveBtnClick()
    {
        if (GameManager.Instance.ReviveByCoin())
        {
            Hide();
        }
    }

    private void OnGiveUpBtnClick()
    {
        if (AdWrapper.Instance.CanShowInterstitial())
        {
            AdWrapper.Instance.ShowInterstitial((isSuccess) =>
            {
                UISystem.Instance.ShowPanel<GiveUpPopup>();
                Hide();

            }, Location.GameEnd);
        }
        else
        {
            UISystem.Instance.ShowPanel<GiveUpPopup>();
            Hide();
        }
    }
}
