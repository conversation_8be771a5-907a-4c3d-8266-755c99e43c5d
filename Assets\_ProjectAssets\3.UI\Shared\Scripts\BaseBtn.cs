using UnityEngine;
using DG.Tweening;
using UnityEngine.EventSystems;
using System;
using UnityEngine.UI;
using DSDK.Vibration;
using DSDK.Audio;
using DSDK.Data;

public class BaseBtn : BaseUIAnim, IPointerDownHandler, IPointerUpHandler
{
    protected Button _button;
    protected bool canInvokeAction = true;
    [SerializeField] private Sprite normalSprite;
    [SerializeField] private Sprite cantInvokeActionSprite;

    // [SerializeField] TextMeshProUGUI[] textInBtn;
    // [SerializeField] Color enableColor;
    // [SerializeField] Color disableColor;
    // [SerializeField] TextMeshProUGUI[] textWithMatInBtn;
    // [SerializeField] Material enableMaterial;
    // [SerializeField] Material disableMaterial;
    public bool CanInvokeAction
    {
        get => canInvokeAction;
        set
        {
            canInvokeAction = value;
            if (normalSprite == null)
            {
                normalSprite = _button.image.sprite;
            }
            _button.image.sprite = canInvokeAction ? normalSprite : cantInvokeActionSprite;
        }
    }
    protected override void Awake()
    {
        base.Awake();
        if (_button == null)
        {
            _button = GetComponent<Button>();
        }
        if (normalSprite == null)
        {
            normalSprite = _button.image.sprite;
        }
    }

    public virtual void OnClick(Action onClick, Action onCantInvokeAction = null)
    {
        if (_button == null)
        {
            _button = GetComponent<Button>();
        }
        _button?.onClick.RemoveAllListeners();
        onClick += () =>
        {
            AudioServer.I.Shot(eAudio.SFX_UI.ToString());
            VibrationManager.Instance.Vibrate(HapticType.Large, 250);

        };
        onCantInvokeAction += () =>
        {
            AudioServer.I.Shot(eAudio.SFX_UI.ToString());
            VibrationManager.Instance.Vibrate(HapticType.Large, 250);

        };
        _button?.onClick.AddListener(() => onClick?.Invoke());
    }
    public virtual void OnPointerDown(PointerEventData eventData)
    {
        if (_button.interactable)
        {
            DOTween.Kill(_scaleTween);
            if (_idleScaleTween != null && _idleScaleTween.IsPlaying())
            {
                _idleScaleTween.Pause();
            }
            _scaleTween = _rect.DOScale(0.9f * _originScale, 0.1f).SetEase(Ease.OutBack);
        }
    }

    public virtual void OnPointerUp(PointerEventData eventData)
    {
        if (_button.interactable)
        {
            DOTween.Kill(_scaleTween);
            if (_idleScaleTween != null && !_idleScaleTween.IsPlaying())
            {
                _idleScaleTween.Play();
            }
            _scaleTween = _rect.DOScale(_originScale, 0.1f);
        }
    }


    void OnValidate()
    {
        if (_button == null)
        {
            _button = GetComponent<Button>();
        }
        if (_button != null && _button.image.sprite != null && normalSprite == null)
        {
            normalSprite = _button.image.sprite;
        }
    }

    protected override void OnDisable()
    {
        base.OnDisable();
        DOTween.Kill(_scaleTween);
    }
    protected override void OnDestroy()
    {
        base.OnDestroy();
        DOTween.Kill(_scaleTween);
    }

}