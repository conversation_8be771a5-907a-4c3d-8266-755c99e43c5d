using Tools.Converter.CrowExpress;
using UnityEditor;
using UnityEngine;

namespace DSDK.LevelEditor
{
    public static class VehicleHelper
    {
        public static GameObject ChangeVehicleType(VehicleAssetData vehicleAssetData, ColorManager colorManager, Vehicle selectedVehicle, EVehicleType newType)
        {
            if (selectedVehicle == null) return null;
            var editingVehicleColor = selectedVehicle.Color;

            // Store current vehicle data
            Vector3 currentPosition = selectedVehicle.transform.localPosition;
            Quaternion currentRotation = selectedVehicle.transform.localRotation;
            Vector3 currentScale = selectedVehicle.transform.localScale;
            Transform parent = selectedVehicle.transform.parent;

            // Get current vehicle data for length
            int vehicleLength = selectedVehicle.Length;

            // Find the new vehicle prefab
            var vehicleAsset = vehicleAssetData.GetVehiclePrefab(newType, vehicleLength);
            if (vehicleAsset?.Prefab == null)
            {
                EditorUtility.DisplayDialog("Error", $"No prefab found for vehicle type {newType} with length {vehicleLength}", "OK");
                return null;
            }

            // Create new vehicle
            GameObject newVehicleInstance = Object.Instantiate(vehicleAsset.Prefab.gameObject, parent);
            newVehicleInstance.transform.localPosition = currentPosition;
            newVehicleInstance.transform.localRotation = currentRotation;
            newVehicleInstance.transform.localScale = currentScale;

            // Set up the new vehicle
            var newVehicle = newVehicleInstance.GetComponent<Vehicle>();
            if (newVehicle != null)
            {
                var vehicleData = selectedVehicle.GetData();
                vehicleData.vehicleType = newType;
                vehicleData.Length = vehicleLength;
                newVehicle.SetData(vehicleData);
                SetData(newVehicle, colorManager, vehicleData);
                newVehicleInstance.name = $"Vehicle_{vehicleData.vehicleType}_{vehicleData.Length}_{vehicleData.color}";
            }

            // Delete the old vehicle
            if (Application.isPlaying)
            {
                Object.Destroy(selectedVehicle.gameObject);
            }
            else
            {
                Object.DestroyImmediate(selectedVehicle.gameObject);
            }

            return newVehicleInstance;
        }

        public static void ChangeVehicleColor(ColorManager colorManager, Vehicle selectedVehicle, EColor newColor)
        {
            if (selectedVehicle == null) return;

            // Get the material for the new color
            var material = colorManager.GetColor(newColor);
            if (material == null)
            {
                Debug.LogWarning($"No material found for color {newColor}");
                return;
            }

            var data = selectedVehicle.GetData();
            data.color = newColor;
            selectedVehicle.SetData(data);

            // Set the new material on the vehicle
            selectedVehicle.SetMaterial(material);
        }

        public static GameObject ChangeVehicleSize(VehicleAssetData vehicleAssetData, ColorManager colorManager, Vehicle selectedVehicle, int newSize)
        {
            if (selectedVehicle == null) return null;

            // Get the new vehicle prefab based on the new size
            var vehicleAsset = vehicleAssetData.GetVehiclePrefab(selectedVehicle.VehicleType, newSize);
            if (vehicleAsset?.Prefab == null)
            {
                EditorUtility.DisplayDialog("Error", $"No prefab found for vehicle type {selectedVehicle.VehicleType} with length {newSize}", "OK");
                return null;
            }

            var parent = selectedVehicle.transform.parent;

            var vehicleData = selectedVehicle.GetData();

            // Create new vehicle instance with the new size
            GameObject newVehicleInstance = Object.Instantiate(vehicleAsset.Prefab.gameObject, parent);
            newVehicleInstance.name = $"Vehicle_{vehicleData.vehicleType}_{vehicleData.Length}_{vehicleData.color}";
            newVehicleInstance.transform.localPosition = vehicleData.Position;
            newVehicleInstance.transform.localRotation = Quaternion.Euler(0, vehicleData.AngleY, 0);

            var newVehicle = newVehicleInstance.GetComponent<Vehicle>();
            if (newVehicle != null)
            {
                vehicleData.Length = newSize;
                newVehicle.SetData(vehicleData);
                SetData(newVehicle, colorManager, vehicleData);
            }

            // Delete the old vehicle
            if (Application.isPlaying)
            {
                Object.Destroy(selectedVehicle.gameObject);
            }
            else
            {
                Object.DestroyImmediate(selectedVehicle.gameObject);
            }
            return newVehicleInstance;
        }

        public static void CreateVehicleFromData(VehicleAssetData vehicleAssetData, ColorManager colorManager, VehicleData vehicleData, int index, Transform container)
        {
            var vehicleAsset = vehicleAssetData.GetVehiclePrefab(vehicleData.vehicleType, vehicleData.Length);
            if (vehicleAsset?.Prefab == null)
            {
                Debug.LogWarning($"No prefab found for vehicle type {vehicleData.vehicleType} with length {vehicleData.Length}");
                return;
            }

            GameObject vehicleInstance = Object.Instantiate(vehicleAsset.Prefab.gameObject, container.transform);
            vehicleInstance.name = $"Vehicle_{index}_{vehicleData.vehicleType}_{vehicleData.color}";
            vehicleInstance.transform.localPosition = vehicleData.Position;
            vehicleInstance.transform.localRotation = Quaternion.Euler(0, vehicleData.AngleY, 0);

            var vehicle = vehicleInstance.GetComponent<Vehicle>();
            if (vehicle != null)
            {
                SetData(vehicle, colorManager, vehicleData);
            }
        }

        public static (Vehicle, GameObject) CreateVehicle(VehicleAssetData vehicleAssetData, ColorManager colorManager, VehicleData vehicleData, Transform container)
        {
            var vehicleAsset = vehicleAssetData.GetVehiclePrefab(vehicleData.vehicleType, vehicleData.Length);
            if (vehicleAsset?.Prefab == null)
            {
                Debug.LogWarning($"No prefab found for vehicle type {vehicleData.vehicleType} with length {vehicleData.Length}");
                return (null, null);
            }

            GameObject vehicleInstance = Object.Instantiate(vehicleAsset.Prefab.gameObject, container);
            vehicleInstance.name = $"Vehicle_{vehicleData.vehicleType}_{vehicleData.color}";
            vehicleInstance.transform.localPosition = vehicleData.Position;
            vehicleInstance.transform.localRotation = Quaternion.Euler(0, vehicleData.AngleY, 0);

            var vehicle = vehicleInstance.GetComponent<Vehicle>();
            if (vehicle != null)
            {
                SetData(vehicle, colorManager, vehicleData);

            }

            return (vehicle, vehicleInstance);
        }

        private static void SetData(Vehicle vehicle, ColorManager colorManager, VehicleData vehicleData)
        {
            vehicle.SetData(vehicleData);
            Debug.Log($"Set data {vehicleData} - {vehicle}");
            if (colorManager == null)
            {
                return;
            }
            var type = vehicleData.vehicleType;
            if (type == EVehicleType.NormalVehicle)
            {
                var material = colorManager.GetColor(vehicleData.color);
                vehicle.SetMaterial(material);
            }

            if (type == EVehicleType.MysteryVehicle)
            {
                var material = colorManager.GetMysteryVehicleColor();
                vehicle.SetMaterial(material);
                vehicle.SetMysteryState(true);
            }
        }

        public static Vehicle[] GetAllVehicles(Transform container)
        {
            var vehicles = container.GetComponentsInChildren<Vehicle>();
            return vehicles;
        }

        public static Gate CreateGateFromData(GarageData garageData, VehicleAssetData assetData, int index, Transform container)
        {
            var gatePrefab = assetData.Gate.Prefab.gameObject;
            if (gatePrefab == null)
            {
                Debug.LogWarning($"No prefab found for gate type");
                return null;
            }

            GameObject gateInstance = UnityEngine.Object.Instantiate(gatePrefab, container);
            gateInstance.name = $"Gate_{index}";

            var gate = gateInstance.GetComponent<Gate>();
            if (gate != null)
            {
                gate.SetData(garageData);
            }
            else
            {
                Debug.LogWarning("Gate component not found on the instantiated gate object.");
            }

            return gate;
        }

        public static LevelSO SaveVehiclesAndGates(LevelSO level, Transform container)
        {
            if (level == null)
            {
                EditorUtility.DisplayDialog("Error", "No level selected. Please select a level first.", "OK");
                return null;
            }

            var vehicleComponents = container.GetComponentsInChildren<Vehicle>();
            if (vehicleComponents.Length == 0)
            {
                EditorUtility.DisplayDialog("Warning", "No vehicles found in the scene to save.", "OK");
                return null;
            }

            level.Vehicles.Clear();
            foreach (var vehicle in vehicleComponents)
            {
                var vehicleData = new VehicleData
                {
                    color = vehicle.Color,
                    vehicleType = vehicle.VehicleType,
                    vehicleDirection = vehicle.VehicleDirection,
                    Width = vehicle.Width,
                    Length = vehicle.Length,
                    Top = vehicle.Top,
                    Left = vehicle.Left,
                    Position = vehicle.transform.localPosition,
                    AngleY = vehicle.transform.localEulerAngles.y,
                    IsNoneGridCar = false
                };
                level.Vehicles.Add(vehicleData);
            }

            level.Garages.Clear();
            var gateComponent = container.GetComponentsInChildren<Gate>();
            foreach (var gate in gateComponent)
            {
                level.Garages.Add(gate.GetGarageData());
            }

            EditorUtility.SetDirty((Object)level);
            AssetDatabase.SaveAssets();
            return level;
        }

        public static Color GetColorFromEColor(EColor color, EVehicleType type = EVehicleType.NormalVehicle)
        {
            if (type != EVehicleType.NormalVehicle && type != EVehicleType.MysteryVehicle)
            {
                switch (type)
                {
                    case EVehicleType.FireTruck:
                        return new Color(1f, 0.047f, 0f); // FF0C00
                    case EVehicleType.Limousine:
                        return new Color(0.086f, 0.094f, 0.18f); // 16182E
                }
            }

            switch (color)
            {
                case EColor.Blue: return new Color(0.082f, 0.286f, 0.765f); // 1549C3
                case EColor.Red: return new Color(1f, 0.047f, 0f); // FF0C00
                case EColor.DarkGreen: return new Color(0.094f, 0.725f, 0.122f); // 18B91F
                case EColor.Yellow: return new Color(0.8f, 0.133f, 0.278f); // CC2247
                case EColor.Orange: return new Color(0.878f, 0.506f, 0.11f); // E0811C
                case EColor.Purple: return new Color(0.635f, 0.282f, 0.953f); // A248F3
                case EColor.Pink: return new Color(0.886f, 0.329f, 0.616f); // E2549D
                case EColor.Gray: return new Color(0.561f, 0.631f, 0.651f); // 8FA1A6
                case EColor.Brown: return new Color(0.498f, 0.239f, 0.067f); // 7F3D11
                case EColor.LightBlue: return new Color(0.141f, 0.694f, 0.773f); // 24B1C5
                default: return Color.white;
            }
        }
    }
}