using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;

public class BaseUIAnim : MonoBehaviour
{
    [System.Serializable]
    public class FadeInSettings
    {
        public bool isFadeIn = false;
        public CanvasGroup canvasGroup;
        public float fadeInDuration = 0.1f;
        public Ease easeFadeIn = Ease.OutBack;
    }

    [System.Serializable]
    public class ScaleSettings
    {
        public bool isSetScale = false;
        public Vector3 startScaleValue = Vector3.zero;
        public float scaleDuration = 0.1f;
        public Ease easeScale = Ease.OutBack;
    }

    [System.Serializable]
    public class PositionSettings
    {
        public bool isSetPos = false;
        public Transform startPos;
        public float moveDuration = 0.5f;
        public Ease easeMove = Ease.OutBack;
    }

    [System.Serializable]
    public class AnimationSettings
    {
        public bool setUpdate = false;
        public float delay = 0f;
    }

    [System.Serializable]
    public class IdleScaleSettings
    {
        public bool isIdleScale = false;
        public float idleScaleDuration = 1f;
        public Ease idleEase = Ease.Linear;
    }

    [SerializeField] private FadeInSettings fadeInSettings = new FadeInSettings();

    [SerializeField] private ScaleSettings scaleSettings = new ScaleSettings();

    [SerializeField] private PositionSettings positionSettings = new PositionSettings();

    [SerializeField] private AnimationSettings animationSettings = new AnimationSettings();

    [SerializeField] private IdleScaleSettings idleScaleSettings = new IdleScaleSettings();

    protected Vector3 _originScale;
    protected Vector2 _originPos;
    protected RectTransform _rect;

    protected Tween _scaleTween;
    protected Tween _moveTween;
    protected Tween _idleScaleTween;

    protected virtual void Awake()
    {
        _rect = GetComponent<RectTransform>();
        _originPos = _rect.anchoredPosition;
        _originScale = _rect.localScale;
    }

    protected virtual void OnEnable()
    {
        DOTween.Kill(transform);

        _rect.localScale = _originScale;

        if (_rect == null)
        {
            _rect = GetComponent<RectTransform>();
        }

        if (fadeInSettings.isFadeIn && fadeInSettings.canvasGroup != null)
        {
            FadeIn();
        }
        if (scaleSettings.isSetScale)
        {
            _rect.localScale = scaleSettings.startScaleValue;
        }

        if (positionSettings.isSetPos && positionSettings.startPos != null && positionSettings.moveDuration > 0)
        {
            _rect.position = positionSettings.startPos.position;
            _moveTween = _rect.DOAnchorPos(_originPos, positionSettings.moveDuration)
                .SetUpdate(animationSettings.setUpdate)
                .SetEase(positionSettings.easeMove);
        }

        if (scaleSettings.isSetScale)
        {
            _scaleTween = _rect.DOScale(_originScale, scaleSettings.scaleDuration)
                .SetUpdate(animationSettings.setUpdate)
                .SetDelay(animationSettings.delay)
                .SetEase(scaleSettings.easeScale)
                .OnComplete(() =>
                {
                    if (idleScaleSettings.isIdleScale)
                    {
                        _idleScaleTween = _rect.DOScale(_originScale * 1.1f, idleScaleSettings.idleScaleDuration)
                            .SetUpdate(animationSettings.setUpdate)
                            .SetEase(idleScaleSettings.idleEase)
                                .SetLoops(-1, LoopType.Yoyo);
                    }
                });
        }
    }
    private void FadeIn()
    {
        fadeInSettings.canvasGroup.alpha = 0;
        fadeInSettings.canvasGroup.DOFade(1, fadeInSettings.fadeInDuration)
            .SetUpdate(animationSettings.setUpdate)
            .SetEase(fadeInSettings.easeFadeIn);
    }

    protected virtual void OnDisable()
    {
        DOTween.Kill(_scaleTween);
        DOTween.Kill(_moveTween);
        DOTween.Kill(_idleScaleTween);
    }
    protected virtual void OnDestroy()
    {
        DOTween.Kill(_scaleTween);
        DOTween.Kill(_moveTween);
        DOTween.Kill(_idleScaleTween);
    }
}
