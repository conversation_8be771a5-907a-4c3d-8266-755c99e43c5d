using TMPro;
using UnityEngine;

public class IAPPackItem : MonoBeh<PERSON>our
{
    [SerializeField] private TextMeshProUGUI[] rewardContext;
    [SerializeField] private GameObject[] glowEff;
    [SerializeField] private GameObject[] blinkEff;

    public void InitData(IAPItemData iapItemData)
    {
        for (int i = 0; i < rewardContext.Length; i++)
        {
            rewardContext[i].text = iapItemData.rewardContext;
        }
        SetGlowEff(iapItemData.isGlowIcon);
        SetBlinkEff(iapItemData.isBlinkIcon);
    }

    public void SetGlowEff(bool isGlow)
    {
        for (int i = 0; i < glowEff.Length; i++)
        {
            glowEff[i].SetActive(isGlow);
        }
    }

    public void SetBlinkEff(bool isBlink)
    {
        for (int i = 0; i < blinkEff.Length; i++)
        {
            blinkEff[i].SetActive(isBlink);
        }
    }

}
