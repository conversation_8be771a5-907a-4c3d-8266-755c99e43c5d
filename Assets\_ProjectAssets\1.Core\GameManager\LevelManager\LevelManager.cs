using System.Collections;
using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using DSDK.Core;
using DSDK.Data;
using DSDK.UISystem;
using TMPro;
using UnityEngine;

public class LevelManager : MonoBehaviour
{
     private GridManager gridManager;
     // [SerializeField] private LevelSO[] levels;
     [SerializeField] private VehicleAssetData vehicleAssetData;
     public VehicleAssetData VehicleAssetData => vehicleAssetData;
     [SerializeField] private ColorManager colorManager;
     public ColorManager ColorManager => colorManager;
     [SerializeField] private Material mysteryMaterial;
     public Material MysteryMaterial => mysteryMaterial;
     [SerializeField] Transform[] bottomCorner;
     [SerializeField] private List<Vehicle> vehicles;
     [SerializeField] private LevelSetSO levelSet;
     [SerializeField] private GameObject tutorialPrefab;
     [SerializeField] private TutorialObject tutorial;
     public int levelsCount => levelSet.levels.Length;

     private LevelSO levelData;
     public LevelSO LevelData => levelData;

     public bool IsHardLevel
     {
          get;
          private set;
     }

     public int TotalHumanoidCount => levelData.Humanoids.Count;
     public int RemainVehicleCount => vehicles.Count(v => !v.WasParked && !v.IsMoving);

     public Vehicle[] GetNonParkingVehicles() => vehicles.Where(v => !v.WasParked && !v.IsMoving).ToArray();
     public Vehicle[] GetSwapableVehicles() => vehicles.Where(v => v.CanSwap()).ToArray();

     public Vector2 GetGridSize()
     {
          return new Vector2(gridManager.Width, gridManager.Height);
     }
     public void IncreaseLevel()
     {
          GameData.Instance.CurrentLevelIndex++;
          if (GameData.Instance.CurrentLevelIndex > levelSet.levels.Length)
          {
               GameData.Instance.LoopLevelIndex = Random.Range(10, levelSet.levels.Length + 1);
          }
     }
     public void Load(bool clear = true)
     {
          GameManager.Instance.SetGameState(EGameState.Loading);
          gridManager = GameManager.Instance.GridManager;
          if (clear)
          {
               ClearVehicles();
          }

          int levelIndex = GameData.Instance.CurrentLevelIndex > levelSet.levels.Length ? GameData.Instance.LoopLevelIndex : GameData.Instance.CurrentLevelIndex;

          levelData = levelSet.levels[levelIndex - 1];
          ShowGamePlayMenu();

          GameManager.Instance.CameraManager.SetCameraSettings(levelData.CameraType);


          if (levelData.CameraType == ELevelCamera.UltraView)
          {
               for (int i = 0; i < bottomCorner.Length; i++)
               {
                    bottomCorner[i].localPosition = new Vector3(bottomCorner[i].localPosition.x, bottomCorner[i].localPosition.y, -24.5f);
               }
          }
          else
          {
               for (int i = 0; i < bottomCorner.Length; i++)
               {
                    bottomCorner[i].localPosition = new Vector3(bottomCorner[i].localPosition.x, bottomCorner[i].localPosition.y, -15.5f);
               }


          }
          gridManager.transform.localScale = Vector3.one * levelData.levelScaleFactor;
          gridManager.Width = levelData.width;
          gridManager.Height = levelData.height;
          gridManager.UpdateGridSize();
          gridManager.ClearAllOccupiedCells();
          StartCoroutine(CreateVehiclesCoroutine());

          foreach (var garageData in levelData.Garages)
          {
               var gateAsset = vehicleAssetData.Gate;
               var gateObject = gateAsset.Prefab.gameObject.Reuse(gridManager.transform);
               var gate = gateObject.GetComponent<Gate>();
               gate.Initialize(garageData);
          }
     }

     private IEnumerator CreateVehiclesCoroutine()
     {
          CheckUIBeforeStart();
          for (int i = 0; i < levelData.Vehicles.Count; i++)
          {
               var vehicleData = levelData.Vehicles[i];
               CreateVehicle(vehicleData, i);
               yield return new WaitForSeconds(0.01f);
          }


          OnGenerateComplete();
     }
     private void CheckUIBeforeStart()
     {
          if (GameData.Instance.CurrentLevelIndex <= 4)
          {
               UISystem.Instance.ShowPanel<LevelIntroOverlay>().SetHumanoidCount(levelData.Humanoids.Count);
          }

          if (levelSet.IsHardLevel(GameData.Instance.CurrentLevelIndex))
          {
               IsHardLevel = true;
               UISystem.Instance.ShowPanel<HardLevelOverlay>().DelayHide(() =>
               {
                    // OnCheckComplete();
               });
          }
          else
          {
               IsHardLevel = false;
               // OnCheckComplete();
          }
     }
     private void OnGenerateComplete()
     {
          GameManager.Instance.SetGameState(EGameState.Playing);

          this.DispatchEvent(EventID.OnLevelLoaded);
          if (levelData.Tutorials.Count > 0)
          {
               ShowTutorial();
          }

          var newContent = GameManager.Instance.GameConfigManager.newContentInfo.GetNewContent(GameData.Instance.CurrentLevelIndex);
          if (newContent != null)
          {
               UISystem.Instance.ShowPanel<NewContentOverlay>().InitNewContent(newContent);
          }
     }
     private void ShowGamePlayMenu()
     {
          UISystem.Instance.ShowPanel<GamePlayMenu>();

          // GamePlayMenu.Instance().SetLevelText(GameData.Instance.CurrentLevelIndex);
     }

     public void ClearVehicles()
     {
          foreach (var vehicle in vehicles)
          {
               if (vehicle != null)
               {
                    vehicle.gameObject.Release();
               }
          }
          vehicles.Clear();
     }

     public void ShowTutorial()
     {
          if (levelData.Tutorials.Count == 0)
          {
               return;
          }

          var tutInfo = levelData.Tutorials[0];
          if (tutInfo.vehicleIndex == -1 && tutInfo.vehicleIndex >= vehicles.Count)
          {
               Debug.LogError("Tutorial vehicle index is out of range");
               return;
          }

          var vehicle = vehicles[tutInfo.vehicleIndex];
          var tutorialObject = Instantiate(tutorialPrefab, vehicle.transform);
          tutorialObject.gameObject.SetActive(true);
          tutorial = tutorialObject.GetComponent<TutorialObject>();
          tutorial.vehicle = vehicle;
          GameManager.Instance.SetTutorial(tutorial);
     }

     public void CreateVehicle(VehicleData vehicleData, int index)
     {
          var vehicleAsset = vehicleAssetData.GetVehiclePrefab(vehicleData.vehicleType, vehicleData.Length);
          var vehicleObject = vehicleAsset.Prefab.gameObject.Reuse(gridManager.transform);
          // vehicleObject.transform.localScale = Vector3.zero;
          // vehicleObject.transform.DOScale(Vector3.one, 0.5f).SetEase(Ease.OutBack);
          DoAnimationVehicle(vehicleObject);
          vehicleObject.name = $"Vehicle_{index}";

          // if (vehicleAsset.ScaleAdjustment > 0)
          // {
          //      vehicleObject.transform.localScale = Vector3.one * vehicleAsset.ScaleAdjustment;
          // }

          var vehicle = vehicleObject.GetComponent<Vehicle>();
          vehicle.Load(vehicleData);
          vehicles.Add(vehicle);
     }
     private void DoAnimationVehicle(GameObject vehicle)
     {
          vehicle.transform.localScale = Vector3.zero;
          vehicle.transform.DOScale(Vector3.one, 0.2f).SetEase(Ease.OutBack);
          vehicle.transform.DOMoveY(vehicle.transform.position.y + 1, 0.2f).SetEase(Ease.OutBack).OnComplete(() =>
          {
               vehicle.transform.DOMoveY(vehicle.transform.position.y - 1, 0.2f).SetEase(Ease.OutBack);
          });
     }

     public Vehicle[] GetNoneBlockedVehicles()
     {
          return vehicles.Where(v => !v.IsBlocked()).ToArray();
     }

     public Vehicle GetRandomNoneBlockedVehicle()
     {
          var vehicles = GetNoneBlockedVehicles();
          if (vehicles.Length == 0)
          {
               return null;
          }
          return vehicles[Random.Range(0, vehicles.Length)];
     }

     /// <summary>
     /// Get vehicles that can swap and are blocked other vehicles
     /// </summary>
     /// <param name="humanoidBlocks"></param>
     /// <returns></returns>
     public Vehicle[] GetVehiclesByHumanoidBlock(List<HumanoidBlock> humanoidBlocks)
     {
          var vehicleTypes = humanoidBlocks.Select(h => h.TargetVehicleType).Distinct().ToList();
          var vehicleColors = humanoidBlocks.Select(h => h.Color).Distinct().ToList();
          return vehicles.Where(v => (
               v.CanSwap() && v.IsBlocked() &&
               (
                    (v.VehicleType == EVehicleType.NormalVehicle && vehicleColors.Contains(v.Color)) ||
                    (v.VehicleType != EVehicleType.NormalVehicle && vehicleTypes.Contains(v.VehicleType))
               )
          )).ToArray();
     }

     public Vehicle GetRandomVehicleByHumanoidBlock(List<HumanoidBlock> humanoidBlocks)
     {
          var matchedVehicles = GetVehiclesByHumanoidBlock(humanoidBlocks);
          if (matchedVehicles.Length == 0)
          {
               return null;
          }
          return matchedVehicles[Random.Range(0, matchedVehicles.Length)];
     }

     public Vehicle GetRandomVehicleByLength(int length, Vehicle excludeVehicle)
     {
          var nonBlockedVehicles = GetNoneBlockedVehicles();
          var vehiclesByLength = nonBlockedVehicles
               .Where(v => v.Length == length && v.CanSwap(excludeVehicle))
               .ToList();
          if (vehiclesByLength.Count == 0)
          {
               return null;
          }
          return vehiclesByLength[Random.Range(0, vehiclesByLength.Count)];
     }

     public void ReleaseVehicle(Vehicle vehicle)
     {
          vehicles.Remove(vehicle);
          vehicle.gameObject.Release();

          if (vehicles.Count == 0 && GameManager.Instance.HumanoidFactory.RunningHumanoidCount == 0)
          {
               MonoBehaviourEventExtensions.DispatchEvent(this, EventID.OnWin);
          }
     }

     public Vector3 GetExitPosition(Vehicle vehicle)
     {
          // Get vehicle's world position and forward direction
          Vector3 vehicleWorldPos = vehicle.transform.position;
          Vector3 vehicleWorldDir = vehicle.transform.forward;

          // Transform the ray (origin and direction) into the GridManager's local space
          Vector3 localRayOrigin = gridManager.transform.InverseTransformPoint(vehicleWorldPos);
          Vector3 localRayDir = gridManager.transform.InverseTransformDirection(vehicleWorldDir);

          // Define the grid boundaries as an AABB in local space, centered at origin
          float gridWidth = gridManager.Width * gridManager.cellSize;
          float gridHeight = gridManager.Height * gridManager.cellSize;
          var bounds = new Bounds(Vector3.zero, new Vector3(gridWidth, 0.1f, gridHeight));

          // Perform a ray-AABB intersection test (slab test) to find the exit distance
          Vector3 invDir = new Vector3(1f / localRayDir.x, 1f / localRayDir.y, 1f / localRayDir.z);
          Vector3 t0s = Vector3.Scale(bounds.min - localRayOrigin, invDir);
          Vector3 t1s = Vector3.Scale(bounds.max - localRayOrigin, invDir);

          Vector3 tmin = Vector3.Min(t0s, t1s);
          Vector3 tmax = Vector3.Max(t0s, t1s);

          // The exit time is the smallest of the maximum t-values.
          float exitTime = Mathf.Min(tmax.x, tmax.y, tmax.z);

          // Calculate the exit position in local space
          Vector3 localExitPos = localRayOrigin + localRayDir * exitTime;

          // Transform the local exit position back to world space
          return gridManager.transform.TransformPoint(localExitPos);
     }


     public void ProduceHumanoids(LevelSO levelData)
     {
          levelData.Humanoids.Clear();

          if (levelData.MaxLine == 0)
          {
               return;
          }

          levelData.Humanoids = new List<HumanoidData>(levelData.MaxLine);
          for (int i = 0; i < levelData.MaxLine; i++)
          {
               levelData.Humanoids.Add(new HumanoidData
               {
                    color = EColor.Gray,
                    targetVehicleType = EVehicleType.NormalVehicle
               });
          }

          List<HumanoidData> humanoidDataList = new List<HumanoidData>();

          foreach (var vehicle in vehicles)
          {
               int seatsCount = vehicle.seats.Length;
               for (int i = 0; i < seatsCount; i++)
               {
                    humanoidDataList.Add(new HumanoidData
                    {
                         color = vehicle.Color,
                         targetVehicleType = vehicle.VehicleType == EVehicleType.MysteryVehicle ? EVehicleType.NormalVehicle : vehicle.VehicleType
                    });
               }
          }

          int currentLineIndex = 0;

          for (int i = 0; i < humanoidDataList.Count; i++)
          {
               HumanoidData newHumanoid = humanoidDataList[i];

               int targetLineIndex = currentLineIndex % levelData.MaxLine;

               levelData.Humanoids[targetLineIndex] = newHumanoid;

               currentLineIndex++;
          }

     }

     public static void SortVehicleDataByDistanceFromCenter(List<VehicleData> vehicleDataList, int gridWidth, int gridHeight)
     {
          if (vehicleDataList == null || vehicleDataList.Count == 0) return;

          float gridCenterX = gridWidth / 2f;
          float gridCenterY = gridHeight / 2f;

          vehicleDataList.Sort((v1, v2) =>
          {
               float distance1 = CalculateVehicleDataDistanceFromGridCenter(v1, gridCenterX, gridCenterY);
               float distance2 = CalculateVehicleDataDistanceFromGridCenter(v2, gridCenterX, gridCenterY);

               // Sort from outside to inside (farther distances first)
               return distance2.CompareTo(distance1);
          });
     }


     private static float CalculateVehicleDataDistanceFromGridCenter(VehicleData vehicleData, float gridCenterX, float gridCenterY)
     {
          // Calculate vehicle center position in grid coordinates
          float vehicleCenterX = vehicleData.Left + (vehicleData.Width / 2f);
          float vehicleCenterY = vehicleData.Top + (vehicleData.Length / 2f);

          // For rotated vehicles, adjust dimensions based on direction
          switch (vehicleData.vehicleDirection)
          {
               case GridObject.Direction.Left:
               case GridObject.Direction.Right:
                    // Swap width and length for horizontal orientation
                    vehicleCenterX = vehicleData.Left + (vehicleData.Length / 2f);
                    vehicleCenterY = vehicleData.Top + (vehicleData.Width / 2f);
                    break;
               case GridObject.Direction.UpLeft:
               case GridObject.Direction.UpRight:
               case GridObject.Direction.DownLeft:
               case GridObject.Direction.DownRight:
                    // For diagonal, use max dimension
                    float maxDimension = Mathf.Max(vehicleData.Width, vehicleData.Length);
                    vehicleCenterX = vehicleData.Left + (maxDimension / 2f);
                    vehicleCenterY = vehicleData.Top + (maxDimension / 2f);
                    break;
          }

          // Calculate Euclidean distance from vehicle center to grid center
          float deltaX = vehicleCenterX - gridCenterX;
          float deltaY = vehicleCenterY - gridCenterY;

          return Mathf.Sqrt(deltaX * deltaX + deltaY * deltaY);
     }
}

