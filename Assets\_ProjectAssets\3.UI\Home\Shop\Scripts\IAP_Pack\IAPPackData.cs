
using System.Linq;
using UnityEngine;


[System.Serializable]
public class IAPItemData
{
    public EIAPType IAPType;
    public bool isGlowIcon;
    public bool isBlinkIcon;
    public int rewardAmount;
    public string rewardContext;
}

[CreateAssetMenu(fileName = "IAPPackData", menuName = "IAPPackData")]
public class IAPPackData : ScriptableObject
{
    public string productId;
    public string namePack;
    public bool isBestOffer;
    public string defaultPrice;
    public IAPItemData[] iapItemDatas;

    public IAPItemData GetIAPItemData(EIAPType iapType)
    {
        return iapItemDatas.ToList().Find(x => x.IAPType == iapType);
    }
}
