using DSDK.Core;
using DSDK.Data;
using DSDK.ToastNotification;
using GameAnalyticsSDK.Setup;
using TMPro;
using UnityEngine;

public class IAPPack : MonoBehaviour
{
    public IAPPackData iapPackData;
    [SerializeField] private BaseBtn buyBtn;
    [SerializeField] private TextMeshProUGUI priceText;
    [SerializeField] private GameObject bestOfferIcon;
    protected bool isProcessingPurchase = false;
    protected void Start()
    {
        InitData();
        buyBtn.OnClick(BuyPack);
        RegisterIAPEvents();
        if (IAPManager.I.GetProductById(iapPackData.productId) != null)
        {
            priceText.text = IAPManager.I.GetProductById(iapPackData.productId).metadata.localizedPriceString;
        }
        else
        {
            priceText.text = iapPackData.defaultPrice;
        }
        if (bestOfferIcon != null)
            bestOfferIcon.SetActive(iapPackData.isBestOffer);
    }

    protected void OnDestroy()
    {
        UnregisterIAPEvents();
    }

    private void RegisterIAPEvents()
    {
        IAPManager.I.OnPurchaseSucceeded += OnPurchaseSuccess;
        IAPManager.I.OnPurchaseFailedEvent += OnPurchaseFailed;
    }

    private void UnregisterIAPEvents()
    {
        IAPManager.I.OnPurchaseSucceeded -= OnPurchaseSuccess;
        IAPManager.I.OnPurchaseFailedEvent -= OnPurchaseFailed;
    }

    public virtual void InitData()
    {

    }

    private void BuyPack()
    {
        if (isProcessingPurchase)
        {
            return;
        }
        string id = iapPackData.productId;
        IAPManager.I.PurchaseProduct(id);
    }

    protected virtual void OnPurchaseSuccess(string id)
    {
        if (id != iapPackData.productId)
        {
            return;
        }

        foreach (var item in iapPackData.iapItemDatas)
        {
            switch (item.IAPType)
            {
                case EIAPType.NoAds:
                    GameData.I.IsAdsRemovePurchased = true;
                    this.DispatchEvent(EventID.RemoveAdsPurchased);
                    break;
                case EIAPType.Heart:
                    GameData.I.FreeHeartRemainTime = item.rewardAmount;
                    GameData.I.CurrentHeart = 5;
                    break;
                case EIAPType.Coin:
                    GameData.I.CurrentCoin += item.rewardAmount;
                    break;
                case EIAPType.Booster:
                    GameManager.Instance.ModifyBoosterAmount(BoosterType.Helicopter, item.rewardAmount);
                    GameManager.Instance.ModifyBoosterAmount(BoosterType.Shuffle, item.rewardAmount);
                    GameManager.Instance.ModifyBoosterAmount(BoosterType.Sort, item.rewardAmount);
                    break;
                case EIAPType.Revive:
                    GameManager.Instance.Revive();
                    break;
            }
        }
    }

    protected virtual void OnPurchaseFailed(string id, string error)
    {
        if (id != iapPackData.productId)
        {
            return;
        }

        ToastManager.I.Show(new string[] { "Failed to purchase item" }, ToastManager.ToastItemType.Error, ToastManager.ToastMode.Normal);
    }
}
