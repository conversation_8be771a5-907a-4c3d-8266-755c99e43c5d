using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System.IO;
using Core.LevelDataConverter;
using System;
using Tools.Converter.CrowExpress;

namespace DSDK.LevelEditor
{
    public class LevelEditorWindow : EditorWindow
    {
        [Header("Asset References")]
        [SerializeField] private VehicleAssetData vehicleAssetData;
        [SerializeField] private ColorManager colorManager;
        [SerializeField] private LevelSO selectedLevel;
        [SerializeField] private LevelSetSO levelSet; // Assuming LevelSetSO is a ScriptableObject that holds multiple levels
        [SerializeField] private string levelSaveFolder = "Assets/_ProjectAssets/6.Levels/Resources";

        // EditorPrefs keys for persistent storage
        private const string VEHICLE_ASSET_DATA_KEY = "LevelEditor_VehicleAssetData";
        private const string COLOR_MANAGER_KEY = "LevelEditor_ColorManager";
        private const string LEVEL_SET_KEY = "LevelEditor_LevelSet";
        private const string SELECTED_LEVEL_KEY = "LevelEditor_SelectedLevel";
        private const string LEVEL_SAVE_FOLDER_KEY = "LevelEditor_LevelSaveFolder";

        // Scene validation
        private const string LEVEL_EDITOR_SCENE_NAME = "LevelEditor";

        // ScrollView
        private Vector2 scrollPosition = Vector2.zero;

        // Humanoid Grid
        private Vector2 humanoidGridScrollPosition = Vector2.zero;
        private List<int> selectedCellIndices = new List<int>();
        private bool isDragging = false;
        private List<int> draggedCellIndices = new List<int>();
        private Vector2 dragStartPosition;
        private bool mouseUpHandled = false;
        private const float CELL_SIZE = 40f;
        private const float CELL_SPACING = 5f;
        private const float BLOCK_SPACING = 20f;
        private const int ROWS_PER_BLOCK = 3;
        private Vector2 dragOffset;
        private int targetInsertIndex = -1;
        private float dragAnimationTime = 0f;
        private Rect humanoidGridRect;
        private Dictionary<int, Rect> cellRects = new Dictionary<int, Rect>();
        private int selectedTab = 0;
        private string[] tabNames = { "⚙ Level Settings", "🚗 Vehicles", "👨‍💻 Humanoids" };
        private int targetHumanoidIndex = -1;
        private bool waitingForTarget = false;
        private string waitingForTargetAction = "";

        private Vehicle selectedVehicle = null;
        private EVehicleType editingVehicleType;
        private EColor editingVehicleColor;
        private int editingVehicleSize;

        // Gate editing
        private Gate selectedGate = null;
        private Vector2Int editingGateVehicleSize;
        private bool isEditingGate = false;
        private EColor humanoidsColorToChange;

        [MenuItem("GameTool/Level Editor")]
        public static void ShowWindow()
        {
            GetWindow<LevelEditorWindow>("Level Editor");
        }

        private void OnEnable()
        {
            LoadSavedReferences();
            ObjectChangeEvents.changesPublished += ChangesPublished;
            Selection.selectionChanged += OnSelectionChanged;

        }

        private void OnDisable()
        {
            ObjectChangeEvents.changesPublished -= ChangesPublished;
            Selection.selectionChanged -= OnSelectionChanged;
        }

        void OnDestroy()
        {
            ObjectChangeEvents.changesPublished -= ChangesPublished;
            Selection.selectionChanged -= OnSelectionChanged;
        }

        void OnSelectionChanged()
        {
            CheckSelectedVehicle();
        }

        void ChangesPublished(ref ObjectChangeEventStream stream)
        {
            if (!SceneHelper.IsInLevelEditorScene())
            {
                return;
            }
            var container = GetContainer();
            if (container == null)
            {
                return;
            }

            for (int i = 0; i < stream.length; ++i)
            {
                var type = stream.GetEventType(i);

                if (type == ObjectChangeKind.CreateGameObjectHierarchy)
                {
                    stream.GetCreateGameObjectHierarchyEvent(i, out var createGameObjectHierarchyEvent);
                    var newGameObject = EditorUtility.InstanceIDToObject(createGameObjectHierarchyEvent.instanceId) as GameObject;
                    if (newGameObject != null && (newGameObject.GetComponent<Vehicle>() != null || newGameObject.GetComponent<Gate>() != null))
                    {
                        newGameObject.transform.SetParent(container.transform);
                    }
                }
            }
        }

        private void HandleGlobalMouseEvents()
        {
            Event currentEvent = Event.current;

            if (currentEvent.type == EventType.Layout)
            {
                mouseUpHandled = false;
                dragAnimationTime = Mathf.Max(0, dragAnimationTime - Time.deltaTime);
            }

            if (currentEvent.type == EventType.MouseUp && isDragging && !mouseUpHandled)
            {
                isDragging = false;
                draggedCellIndices.Clear();
                selectedCellIndices.Clear(); // Clear selection on mouse up
                targetInsertIndex = -1;
                dragAnimationTime = 0.3f;
                Repaint();
            }
        }

        private void CheckSelectedVehicle()
        {
            // Check if a vehicle or gate is selected in the hierarchy
            GameObject selectedObject = Selection.activeGameObject;
            if (selectedObject == null)
            {
                // Clear selections
                selectedVehicle = null;
                selectedGate = null;
                isEditingGate = false;
                return;
            }

            // Check for Vehicle component
            Vehicle vehicle = selectedObject.GetComponent<Vehicle>();
            if (vehicle != null)
            {
                selectedVehicle = vehicle;
                editingVehicleType = vehicle.VehicleType;
                editingVehicleColor = vehicle.Color;
                editingVehicleSize = vehicle.Length;
                // Clear gate selection
                selectedGate = null;
                isEditingGate = false;
                Repaint();
                return;
            }

            // Check for Gate component
            Gate gate = selectedObject.GetComponent<Gate>();
            if (gate != null)
            {
                selectedGate = gate;
                InitializeGateEditing(gate);
                isEditingGate = true;
                // Clear vehicle selection
                selectedVehicle = null;
                Repaint();
                return;
            }

            // No vehicle or gate found
            selectedVehicle = null;
            selectedGate = null;
            isEditingGate = false;
        }

        private void InitializeGateEditing(Gate gate)
        {
            // Get data directly from the gate
            var garageData = gate.GetGarageData();
            if (garageData != null)
            {
                editingGateVehicleSize = garageData.VehicleSize;
            }
            else
            {
                // Default values if no garage data found
                editingGateVehicleSize = new Vector2Int(4, 6); // Default vehicle size
            }
        }

        private void LoadSavedReferences()
        {
            string vehicleAssetDataPath = EditorPrefs.GetString(VEHICLE_ASSET_DATA_KEY, "");
            if (!string.IsNullOrEmpty(vehicleAssetDataPath))
            {
                vehicleAssetData = AssetDatabase.LoadAssetAtPath<VehicleAssetData>(vehicleAssetDataPath);
            }

            string colorManagerPath = EditorPrefs.GetString(COLOR_MANAGER_KEY, "");
            if (!string.IsNullOrEmpty(colorManagerPath))
            {
                colorManager = AssetDatabase.LoadAssetAtPath<ColorManager>(colorManagerPath);
            }

            string selectedLevelPath = EditorPrefs.GetString(SELECTED_LEVEL_KEY, "");
            if (!string.IsNullOrEmpty(selectedLevelPath))
            {
                selectedLevel = AssetDatabase.LoadAssetAtPath<LevelSO>(selectedLevelPath);
            }

            string savedFolder = EditorPrefs.GetString(LEVEL_SAVE_FOLDER_KEY, "Assets/_ProjectAssets/6.Levels/Data");
            if (!string.IsNullOrEmpty(savedFolder))
            {
                levelSaveFolder = savedFolder;
            }

            string levelSetPath = EditorPrefs.GetString(LEVEL_SET_KEY, "");
            if (!string.IsNullOrEmpty(levelSetPath))
            {
                levelSet = AssetDatabase.LoadAssetAtPath<LevelSetSO>(levelSetPath);
            }
        }

        private void SaveReferences()
        {
            if (vehicleAssetData != null)
            {
                string path = AssetDatabase.GetAssetPath(vehicleAssetData);
                EditorPrefs.SetString(VEHICLE_ASSET_DATA_KEY, path);
            }

            if (colorManager != null)
            {
                string path = AssetDatabase.GetAssetPath(colorManager);
                EditorPrefs.SetString(COLOR_MANAGER_KEY, path);
            }

            if (selectedLevel != null)
            {
                string path = AssetDatabase.GetAssetPath(selectedLevel);
                EditorPrefs.SetString(SELECTED_LEVEL_KEY, path);
            }

            if (levelSet != null)
            {
                string path = AssetDatabase.GetAssetPath(levelSet);
                EditorPrefs.SetString(LEVEL_SET_KEY, path);
            }

            EditorPrefs.SetString(LEVEL_SAVE_FOLDER_KEY, levelSaveFolder);
        }

        private void CreateNewLevel()
        {
            if (string.IsNullOrEmpty(levelSaveFolder))
            {
                EditorUtility.DisplayDialog("Error", "Please select a save folder first.", "OK");
                return;
            }

            if (!Directory.Exists(levelSaveFolder))
            {
                bool createFolder = EditorUtility.DisplayDialog("Create Folder",
                    $"The folder '{levelSaveFolder}' does not exist. Would you like to create it?", "Create", "Cancel");
                if (createFolder)
                {
                    Directory.CreateDirectory(levelSaveFolder);
                    AssetDatabase.Refresh();
                }
                else
                {
                    return;
                }
            }

            string baseFileName = "LevelData";
            string filePath = Path.Combine(levelSaveFolder, $"{baseFileName}.asset");
            int counter = 1;

            while (File.Exists(filePath))
            {
                filePath = Path.Combine(levelSaveFolder, $"{baseFileName}_{counter}.asset");
                counter++;
            }

            LevelSO newLevel = ScriptableObject.CreateInstance<LevelSO>();
            newLevel.levelScaleFactor = 1f;
            newLevel.width = 54;
            newLevel.height = 54;
            newLevel.MaxLine = 0;
            newLevel.NoneGridLevel = false;
            newLevel.Vehicles = new List<VehicleData>();
            newLevel.Humanoids = new List<HumanoidData>();
            newLevel.Garages = new List<GarageData>();
            newLevel.Tutorials = new List<LevelSO.TutorialData>();

            AssetDatabase.CreateAsset(newLevel, filePath);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            EditorUtility.FocusProjectWindow();
            Selection.activeObject = newLevel;

            selectedLevel = newLevel;
            SaveReferences();
        }

        private void LoadLevel()
        {
            LevelSO levelToLoad = selectedLevel;

            if (levelToLoad == null)
            {
                string levelPath = EditorUtility.OpenFilePanel("Load Level", levelSaveFolder, "asset");
                if (string.IsNullOrEmpty(levelPath))
                {
                    return;
                }

                if (levelPath.StartsWith(Application.dataPath))
                {
                    levelPath = "Assets" + levelPath.Substring(Application.dataPath.Length);
                }
                else if (!levelPath.StartsWith("Assets/"))
                {
                    EditorUtility.DisplayDialog("Error", "Please select a file within the Assets directory.", "OK");
                    return;
                }

                levelToLoad = AssetDatabase.LoadAssetAtPath<LevelSO>(levelPath);
                if (levelToLoad == null)
                {
                    EditorUtility.DisplayDialog("Error", "Failed to load the selected file as a LevelSO.", "OK");
                    return;
                }

                selectedLevel = levelToLoad;
                SaveReferences();
            }

            var levelLoader = FindFirstObjectByType<LevelLoader>();
            if (levelLoader == null)
            {
                EditorUtility.DisplayDialog("Error", "No LevelLoader found in the scene. Please add a LevelLoader component to the scene.", "OK");
                return;
            }

            ClearExistingVehicles(levelLoader);
            InstantiateVehiclesFromLevel(levelToLoad, levelLoader);

            EditorUtility.FocusProjectWindow();
            Selection.activeObject = levelToLoad;
        }

        private void ClearExistingVehicles(LevelLoader levelLoader)
        {
            for (int i = levelLoader.transform.childCount - 1; i >= 0; i--)
            {
                Transform child = levelLoader.transform.GetChild(i);
                if (Application.isPlaying)
                {
                    Destroy(child.gameObject);
                }
                else
                {
                    DestroyImmediate(child.gameObject);
                }
            }
        }

        private void InstantiateVehiclesFromLevel(LevelSO levelData, LevelLoader levelLoader)
        {
            if (levelData.Vehicles == null || levelData.Vehicles.Count == 0)
            {
                Debug.LogWarning("No vehicles found in the loaded level data.");
                return;
            }

            if (vehicleAssetData == null)
            {
                EditorUtility.DisplayDialog("Error", "VehicleAssetData is not assigned. Please assign it in the Level Settings.", "OK");
                return;
            }

            for (int i = 0; i < levelData.Vehicles.Count; i++)
            {
                var vehicleData = levelData.Vehicles[i];
                VehicleHelper.CreateVehicleFromData(vehicleAssetData, colorManager, vehicleData, i, levelLoader.transform);
            }

            for (int i = 0; i < levelData.Garages.Count; i++)
            {
                var gateData = levelData.Garages[i];
                VehicleHelper.CreateGateFromData(gateData, vehicleAssetData, i, levelLoader.transform);
            }

            Debug.Log($"Instantiated {levelData.Vehicles.Count} vehicles from level: {levelData.name}");
        }

        private void OnGUI()
        {
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            GUILayout.Label("Level Editor", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            // Scene Warning Section
            if (!SceneHelper.IsInLevelEditorScene())
            {
                EditorGUILayout.HelpBox("⚠️ You are not in the LevelEditor scene. Some features may not work correctly.", MessageType.Warning);
                EditorGUILayout.BeginHorizontal();
                GUILayout.FlexibleSpace();
                if (GUILayout.Button("Open LevelEditor Scene", GUILayout.Height(30), GUILayout.Width(200)))
                {
                    SceneHelper.OpenLevelEditorScene();
                }
                GUILayout.FlexibleSpace();
                EditorGUILayout.EndHorizontal();
                EditorGUILayout.Space();
            }
            else
            {
                EditorGUILayout.HelpBox("✅ You are in the LevelEditor scene. All features are available.", MessageType.Info);
                EditorGUILayout.Space();
            }

            // Draw tabs with increased height
            GUI.backgroundColor = Color.blue;
            selectedTab = GUILayout.Toolbar(selectedTab, tabNames, GUILayout.Height(35));
            GUI.backgroundColor = Color.white;
            EditorGUILayout.Space();

            // Draw content based on selected tab
            switch (selectedTab)
            {
                case 0: // Level Settings
                    DrawLevelSettingsTab();
                    break;
                case 1: // Vehicles
                    DrawVehiclesTab();
                    break;
                case 2: // Humanoids
                    DrawHumanoidsTab();
                    break;
            }

            // End ScrollView
            EditorGUILayout.EndScrollView();

            // Handle global mouse events at the end
            HandleGlobalMouseEvents();
        }

        private void DrawLevelSettingsTab()
        {
            EditorGUILayout.LabelField("Level Settings", EditorStyles.boldLabel);
            VehicleAssetData newVehicleAssetData = (VehicleAssetData)EditorGUILayout.ObjectField("Vehicle Asset Data", vehicleAssetData, typeof(VehicleAssetData), false);
            if (newVehicleAssetData != vehicleAssetData)
            {
                vehicleAssetData = newVehicleAssetData;
                SaveReferences();
            }

            ColorManager newColorManager = (ColorManager)EditorGUILayout.ObjectField("Color Manager", colorManager, typeof(ColorManager), false);
            if (newColorManager != colorManager)
            {
                colorManager = newColorManager;
                SaveReferences();
            }

            EditorGUILayout.Space();

            EditorGUILayout.LabelField("Level Save Folder", EditorStyles.boldLabel);
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Save Folder:", GUILayout.Width(80));
            EditorGUILayout.TextField(levelSaveFolder, GUILayout.ExpandWidth(true));

            if (GUILayout.Button("Browse", GUILayout.Width(60)))
            {
                string newFolder = EditorUtility.OpenFolderPanel("Select Level Save Folder", levelSaveFolder, "");
                if (!string.IsNullOrEmpty(newFolder))
                {
                    if (newFolder.StartsWith(Application.dataPath))
                    {
                        newFolder = "Assets" + newFolder.Substring(Application.dataPath.Length);
                    }
                    else if (newFolder.StartsWith("Assets/"))
                    {
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("Invalid Folder", "Please select a folder within the Assets directory.", "OK");
                        return;
                    }

                    levelSaveFolder = newFolder;
                    SaveReferences();
                }
            }
            EditorGUILayout.EndHorizontal();

            if (!string.IsNullOrEmpty(levelSaveFolder))
            {
                if (!Directory.Exists(levelSaveFolder))
                {
                    EditorGUILayout.HelpBox("Selected folder does not exist. Please create it or select a valid folder.", MessageType.Warning);
                    if (GUILayout.Button("Create Folder"))
                    {
                        Directory.CreateDirectory(levelSaveFolder);
                        AssetDatabase.Refresh();
                    }
                }
                else
                {
                    EditorGUILayout.HelpBox($"Levels will be saved to: {levelSaveFolder}", MessageType.Info);
                }
            }

            EditorGUILayout.Space();



            EditorGUILayout.LabelField("Level Selection", EditorStyles.boldLabel);
            LevelSO newSelectedLevel = (LevelSO)EditorGUILayout.ObjectField("Selected Level", selectedLevel, typeof(LevelSO), false);
            if (newSelectedLevel != selectedLevel)
            {
                selectedLevel = newSelectedLevel;
                SaveReferences();
            }

            if (SceneHelper.IsInLevelEditorScene())
            {
                EditorGUILayout.LabelField("Level Actions", EditorStyles.boldLabel);
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("New Level", GUILayout.Height(30)))
                {
                    CreateNewLevel();
                }

                if (GUILayout.Button("Load Level", GUILayout.Height(30)))
                {
                    LoadLevel();
                }
                EditorGUILayout.EndHorizontal();
            }
            else
            {
                EditorGUILayout.LabelField("Level Actions", EditorStyles.boldLabel);
                EditorGUILayout.HelpBox("Level actions are only available in the LevelEditor scene.", MessageType.Warning);
            }

            EditorGUILayout.Space();



            LevelSetSO newLevelSet = (LevelSetSO)EditorGUILayout.ObjectField("Level Set", levelSet, typeof(LevelSetSO), false);
            if (newLevelSet != levelSet)
            {
                levelSet = newLevelSet;
                SaveReferences();
            }

            EditorGUILayout.Space();

            if (SceneHelper.IsInLevelEditorScene() && levelSet != null)
            {
                for (int i = 0; i < levelSet.levels.Length; i++)
                {
                    LevelSO level = levelSet.levels[i];
                    if (level == null) continue;

                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField((i + 1).ToString(), GUILayout.Width(20));
                    EditorGUILayout.LabelField(level.name);
                    if (GUILayout.Button("Load", GUILayout.Width(60)))
                    {
                        selectedLevel = level;
                        SaveReferences();
                        LoadLevel();
                    }
                    if (GUILayout.Button("Select", GUILayout.Width(60)))
                    {
                        Selection.activeObject = level;
                    }
                    EditorGUILayout.EndHorizontal();
                    GridHelper.DrawUILine(Color.bisque, 1, 10);
                }
            }
            EditorGUILayout.Space();
        }

        private void DrawVehicleEditingSection()
        {
            if (selectedVehicle == null) return;

            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("🚗 Vehicle Editor", EditorStyles.boldLabel);

            // Vehicle info with color preview
            EditorGUILayout.LabelField($"Selected Vehicle: {selectedVehicle.name}", EditorStyles.miniLabel);
            EditorGUILayout.LabelField($"Current Type: {selectedVehicle.VehicleType}", EditorStyles.miniLabel);
            EditorGUILayout.LabelField($"Current Size: {selectedVehicle.Length}", EditorStyles.miniLabel);

            // Color preview
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Current Color:", EditorStyles.miniLabel);
            Color previewColor = VehicleHelper.GetColorFromEColor(selectedVehicle.Color);
            GUI.color = previewColor;
            GUILayout.Box("", GUILayout.Width(20), GUILayout.Height(20));
            GUI.color = Color.white;
            EditorGUILayout.LabelField(selectedVehicle.Color.ToString(), EditorStyles.miniLabel);
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space();

            // Vehicle Type Editor
            EditorGUI.BeginChangeCheck();
            var data = selectedVehicle.GetData();
            EVehicleType newVehicleType = (EVehicleType)EditorGUILayout.EnumPopup("Type", editingVehicleType);
            if (EditorGUI.EndChangeCheck())
            {
                if (newVehicleType != editingVehicleType)
                {
                    data.vehicleType = newVehicleType;
                    var newVehicleInstance = VehicleHelper.ChangeVehicleType(vehicleAssetData, colorManager, selectedVehicle, newVehicleType);
                    if (newVehicleInstance != null)
                    {
                        // Update selection to the new vehicle
                        Selection.activeGameObject = newVehicleInstance;
                    }
                }
            }

            // Vehicle Size Editor
            if (vehicleAssetData != null)
            {
                var availableSizes = vehicleAssetData.GetAvailableSize(editingVehicleType);
                if (availableSizes != null && availableSizes.Count > 0)
                {
                    //         EditorGUILayout.LabelField("Vehicle Size", EditorStyles.boldLabel);

                    //         // Create size options as strings
                    string[] sizeOptions = new string[availableSizes.Count];
                    int currentSizeIndex = 0;

                    for (int i = 0; i < availableSizes.Count; i++)
                    {
                        sizeOptions[i] = availableSizes[i].ToString();
                        if (availableSizes[i] == editingVehicleSize)
                        {
                            currentSizeIndex = i;
                        }
                    }

                    EditorGUI.BeginChangeCheck();
                    int newSizeIndex = EditorGUILayout.Popup("Size", currentSizeIndex, sizeOptions);
                    if (EditorGUI.EndChangeCheck())
                    {
                        if (newSizeIndex >= 0 && newSizeIndex < availableSizes.Count)
                        {
                            int newSize = availableSizes[newSizeIndex];
                            if (newSize != editingVehicleSize)
                            {
                                editingVehicleSize = newSize;
                                var newVehicle = VehicleHelper.ChangeVehicleSize(vehicleAssetData, colorManager, selectedVehicle, editingVehicleSize);
                                if (newVehicle != null)
                                {
                                    // Update selection to the new vehicle
                                    Selection.activeGameObject = newVehicle;
                                }
                            }
                        }
                    }
                }
            }

            EditorGUILayout.Space();

            // Vehicle Color Editor
            EditorGUILayout.BeginHorizontal();
            EditorGUI.BeginChangeCheck();
            EColor newVehicleColor = (EColor)EditorGUILayout.EnumPopup("Color", editingVehicleColor);
            if (EditorGUI.EndChangeCheck())
            {
                if (newVehicleColor != editingVehicleColor)
                {
                    editingVehicleColor = newVehicleColor;
                    ChangeVehicleColor(newVehicleColor);
                }
            }
            var rect = GUILayoutUtility.GetRect(20, 20, GUILayout.ExpandWidth(false));
            GridHelper.DrawCell(rect, VehicleHelper.GetColorFromEColor(editingVehicleColor));
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space();
            EditorGUILayout.EndVertical();
        }

        private void DrawGateEditingSection()
        {
            if (selectedGate == null) return;

            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("🚪 Gate Editor", EditorStyles.boldLabel);

            // Gate info
            EditorGUILayout.LabelField($"Selected Gate: {selectedGate.name}", EditorStyles.miniLabel);
            var garageData = selectedGate.GetGarageData();
            if (garageData != null)
            {
                EditorGUILayout.LabelField($"Current Vehicle Size: {garageData.VehicleSize.x}x{garageData.VehicleSize.y}", EditorStyles.miniLabel);
                EditorGUILayout.LabelField($"Current Colors Count: {garageData.VehicleColors?.Count ?? 0}", EditorStyles.miniLabel);
            }

            EditorGUILayout.Space();

            // Vehicle Size Editor for Gate
            EditorGUILayout.LabelField("Spawned Vehicle Size", EditorStyles.boldLabel);
            EditorGUI.BeginChangeCheck();

            // Width (X) - using available vehicle sizes
            if (vehicleAssetData != null)
            {
                var availableSizes = vehicleAssetData.GetAvailableSize(EVehicleType.NormalVehicle);
                if (availableSizes != null && availableSizes.Count > 0)
                {
                    string[] sizeOptions = new string[availableSizes.Count];
                    int currentWidthIndex = 0;

                    for (int i = 0; i < availableSizes.Count; i++)
                    {
                        sizeOptions[i] = availableSizes[i].ToString();
                        if (availableSizes[i] == (int)editingGateVehicleSize.x)
                        {
                            currentWidthIndex = i;
                        }
                    }

                    int newWidthIndex = EditorGUILayout.Popup("Vehicle Width", currentWidthIndex, sizeOptions);
                    if (newWidthIndex >= 0 && newWidthIndex < availableSizes.Count)
                    {
                        editingGateVehicleSize.x = availableSizes[newWidthIndex];
                    }

                    // Length (Y) - using available vehicle sizes
                    int currentLengthIndex = 0;
                    for (int i = 0; i < availableSizes.Count; i++)
                    {
                        if (availableSizes[i] == (int)editingGateVehicleSize.y)
                        {
                            currentLengthIndex = i;
                            break;
                        }
                    }

                    int newLengthIndex = EditorGUILayout.Popup("Vehicle Length", currentLengthIndex, sizeOptions);
                    if (newLengthIndex >= 0 && newLengthIndex < availableSizes.Count)
                    {
                        editingGateVehicleSize.y = availableSizes[newLengthIndex];
                    }
                }
                else
                {
                    // Fallback to direct input if no vehicle sizes available
                    editingGateVehicleSize = EditorGUILayout.Vector2IntField("Vehicle Size", editingGateVehicleSize);
                }
            }
            else
            {
                editingGateVehicleSize = EditorGUILayout.Vector2IntField("Vehicle Size", editingGateVehicleSize);
            }

            if (EditorGUI.EndChangeCheck())
            {
                ApplyGateVehicleSizeChange();
            }

            EditorGUILayout.Space();

            // Vehicle Colors Editor (Inspector-like)
            EditorGUILayout.LabelField("Vehicle Colors", EditorStyles.boldLabel);

            if (garageData != null && garageData.VehicleColors != null)
            {
                var vehicleColors = garageData.VehicleColors;

                // Size field (like inspector arrays)
                EditorGUI.BeginChangeCheck();
                int newSize = EditorGUILayout.IntField("Size", vehicleColors.Count);
                if (EditorGUI.EndChangeCheck())
                {
                    // Resize the list
                    while (vehicleColors.Count < newSize)
                    {
                        vehicleColors.Add(EColor.Red); // Default color
                    }
                    while (vehicleColors.Count > newSize)
                    {
                        vehicleColors.RemoveAt(vehicleColors.Count - 1);
                    }
                }

                EditorGUILayout.Space();

                // Display each color element (like inspector arrays)
                if (vehicleColors.Count > 0)
                {
                    for (int i = 0; i < vehicleColors.Count; i++)
                    {
                        EditorGUILayout.BeginHorizontal();

                        // Element label
                        EditorGUILayout.LabelField($"Element {i}", GUILayout.Width(80));

                        // Color enum popup
                        EditorGUI.BeginChangeCheck();
                        EColor newColor = (EColor)EditorGUILayout.EnumPopup(vehicleColors[i]);
                        if (EditorGUI.EndChangeCheck())
                        {
                            vehicleColors[i] = newColor;
                        }

                        // Color preview box
                        GUILayout.Box("", GUILayout.Width(25), GUILayout.Height(18));
                        Color previewColor = VehicleHelper.GetColorFromEColor(vehicleColors[i]);
                        var rect = GUILayoutUtility.GetLastRect();
                        GridHelper.DrawCell(rect, previewColor);

                        // Remove button
                        GUI.backgroundColor = Color.red;
                        if (GUILayout.Button("✖", GUILayout.Width(25), GUILayout.Height(18)))
                        {
                            vehicleColors.RemoveAt(i);
                            break; // Exit loop to avoid index issues
                        }
                        GUI.backgroundColor = Color.white;

                        EditorGUILayout.EndHorizontal();
                    }
                }
                else
                {
                    EditorGUILayout.HelpBox("No colors assigned. Increase size or use buttons below to add colors.", MessageType.Info);
                }

                EditorGUILayout.Space();

                // Quick action buttons
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Add Random Colors", GUILayout.Height(25)))
                {
                    AddRandomColorsToGate();
                }

                GUI.backgroundColor = Color.red;
                if (GUILayout.Button("Clear All", GUILayout.Height(25)))
                {
                    if (EditorUtility.DisplayDialog("Clear All Colors", "Are you sure you want to remove all colors from this gate?", "Clear", "Cancel"))
                    {
                        vehicleColors.Clear();
                    }
                }
                GUI.backgroundColor = Color.green;
                if (GUILayout.Button("Add Color", GUILayout.Height(25)))
                {
                    vehicleColors.Add(EColor.Red);
                }
                GUI.backgroundColor = Color.white;
                EditorGUILayout.EndHorizontal();
            }
            else
            {
                EditorGUILayout.HelpBox("No garage data found for this gate.", MessageType.Warning);
            }

            EditorGUILayout.EndVertical();
        }

        private void DrawHumanoidsTab()
        {
            if (selectedLevel == null)
            {
                EditorGUILayout.HelpBox("Please select a level first in the Level Settings tab.", MessageType.Info);
                return;
            }

            EditorGUILayout.LabelField("Humanoid Management", EditorStyles.boldLabel);

            // Validate humanoid data and show warnings
            ValidateHumanoidData();

            if (SceneHelper.IsInLevelEditorScene())
            {
                HumanoidHelper.DrawLegends();

                EditorGUILayout.BeginHorizontal();
                CreateButton("Generate Humanoids", GenerateHumanoids, Color.aquamarine);


                if (GUILayout.Button("Randomize Humanoids", GUILayout.Height(30)))
                {
                    RandomizeHumanoids();
                }

                // GUI.backgroundColor = Color.green;
                // if (GUILayout.Button("+ Add Humanoid", GUILayout.Height(30)))
                // {
                //     HumanoidHelper.AddNewHumanoid(selectedLevel);
                // }
                // GUI.backgroundColor = Color.white;

                EditorGUILayout.EndHorizontal();
                EditorGUILayout.Space();
            }

            EditorGUILayout.BeginHorizontal();
            CreateButton("Clear Selection", () =>
            {
                selectedCellIndices.Clear();
                isDragging = false;
                draggedCellIndices.Clear();
                targetInsertIndex = -1;
                targetHumanoidIndex = -1;
                waitingForTarget = false;
                waitingForTargetAction = "";
                Repaint();
            }, Color.darkOrange);
            CreateButton("➕ Add Humanoid", () => HumanoidHelper.AddNewHumanoid(selectedLevel), Color.softGreen);

            if (selectedCellIndices.Count > 0)
            {
                var selectedCount = selectedCellIndices.Count;
                CreateButton($"🗑️ Remove Selected ({selectedCount})", RemoveSelectedHumanoids, Color.red);
            }

            EditorGUILayout.EndHorizontal();

            if (selectedCellIndices.Count > 0)
            {
                DrawColorChangeSection();
            }

            // Draw Humanoid Grid
            DrawHumanoidGrid();
            EditorGUILayout.Space();



            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Summarize Humanoids:", EditorStyles.boldLabel);

            Dictionary<EColor, int> colorCounts = new Dictionary<EColor, int>();
            Dictionary<EVehicleType, int> specialHumanoidCounts = new Dictionary<EVehicleType, int>();
            foreach (var humanoid in selectedLevel.Humanoids)
            {
                if (humanoid.targetVehicleType == EVehicleType.NormalVehicle || humanoid.targetVehicleType == EVehicleType.MysteryVehicle)
                {
                    if (!colorCounts.ContainsKey(humanoid.color))
                    {
                        colorCounts[humanoid.color] = 0;
                    }
                    colorCounts[humanoid.color]++;
                }
                else
                {
                    if (!specialHumanoidCounts.ContainsKey(humanoid.targetVehicleType))
                    {
                        specialHumanoidCounts[humanoid.targetVehicleType] = 0;
                    }
                    specialHumanoidCounts[humanoid.targetVehicleType]++;
                }
            }

            foreach (var kvp in colorCounts)
            {
                EditorGUILayout.LabelField($"Color {kvp.Key}: {kvp.Value} humanoids");
            }
            foreach (var kvp in specialHumanoidCounts)
            {
                EditorGUILayout.LabelField($"[Special] {kvp.Key}: {kvp.Value} humanoids");
            }
        }

        private void DrawVehiclesTab()
        {
            if (selectedLevel == null)
            {
                EditorGUILayout.HelpBox("Please select a level first in the Level Settings tab.", MessageType.Info);
                return;
            }

            if (selectedVehicle != null)
            {
                DrawVehicleEditingSection();
                EditorGUILayout.Space();
            }

            if (isEditingGate && selectedGate != null)
            {
                DrawGateEditingSection();
                EditorGUILayout.Space();
            }

            DrawVehiclePrefabsPanel();

            EditorGUILayout.LabelField("Vehicle Management", EditorStyles.boldLabel);

            if (SceneHelper.IsInLevelEditorScene())
            {
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Save Vehicles", GUILayout.Height(30)))
                {
                    VehicleHelper.SaveVehiclesAndGates(selectedLevel, GetContainer());
                }
                if (GUILayout.Button("Randomize Colors", GUILayout.Height(30)))
                {
                    RandomizeVehicleColors();
                }
                EditorGUILayout.EndHorizontal();
                EditorGUILayout.Space();
            }

            // Display vehicle information
            if (selectedLevel.Vehicles != null && selectedLevel.Vehicles.Count > 0)
            {
                EditorGUILayout.LabelField("Summarized Vehicles:", EditorStyles.boldLabel);
                Dictionary<EColor, int> colorCounts = new Dictionary<EColor, int>();
                Dictionary<string, int> humanoidCounts = new Dictionary<string, int>();
                Dictionary<string, int> specialVehicles = new Dictionary<string, int>();
                for (int i = 0; i < selectedLevel.Vehicles.Count; i++)
                {
                    var vehicle = selectedLevel.Vehicles[i];
                    if (vehicle.vehicleType == EVehicleType.MysteryVehicle || vehicle.vehicleType == EVehicleType.NormalVehicle)
                    {
                        if (!colorCounts.ContainsKey(vehicle.color))
                        {
                            colorCounts[vehicle.color] = 0;
                            humanoidCounts[vehicle.color.ToString()] = 0;
                        }
                        colorCounts[vehicle.color]++;
                        humanoidCounts[vehicle.color.ToString()] += vehicle.SlotCount;
                    }
                    else
                    {
                        if (!specialVehicles.ContainsKey(vehicle.vehicleType.ToString()))
                        {
                            specialVehicles[vehicle.vehicleType.ToString()] = 0;
                            humanoidCounts[vehicle.vehicleType.ToString()] = 0;
                        }
                        specialVehicles[vehicle.vehicleType.ToString()]++;
                        humanoidCounts[vehicle.vehicleType.ToString()] += vehicle.SlotCount;
                    }
                    // EditorGUILayout.LabelField($"Vehicle {i}: {vehicle.vehicleType} - Color: {vehicle.color} - Size: {vehicle.Width}x{vehicle.Length}");
                }

                foreach (var kvp in colorCounts)
                {
                    var humanoidCount = humanoidCounts[kvp.Key.ToString()];
                    EditorGUILayout.LabelField($"Color {kvp.Key}: {kvp.Value} vehicles => {humanoidCount} humanoids");
                }

                foreach (var kvp in specialVehicles)
                {
                    var humanoidCount = humanoidCounts[kvp.Key.ToString()];
                    EditorGUILayout.LabelField($"Special Vehicle {kvp.Key}: {kvp.Value} vehicles => {humanoidCount} humanoids");
                }
            }
            else
            {
                EditorGUILayout.HelpBox("No vehicles found in the selected level.", MessageType.Info);
            }
        }

        private void DrawVehiclePrefabsPanel()
        {
            if (vehicleAssetData == null || vehicleAssetData.Vehicles == null)
                return;

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Drag & Drop Vehicle Prefabs into Scene", EditorStyles.boldLabel);

            int columns = 8;
            int count = 0;
            EditorGUILayout.BeginVertical();
            EditorGUILayout.BeginHorizontal();
            foreach (var vehicle in vehicleAssetData.Vehicles)
            {
                if (vehicle.Prefab == null) continue;

                GUILayout.BeginVertical(GUILayout.Width(80));
                // Draw thumbnail
                GUIContent content = EditorGUIUtility.ObjectContent(vehicle.Prefab.gameObject, typeof(GameObject));
                Rect thumbRect = GUILayoutUtility.GetRect(64, 64, GUILayout.ExpandWidth(false), GUILayout.ExpandHeight(false));
                GUI.Box(thumbRect, GUIContent.none); // border
                GUI.Label(thumbRect, content.image, GUIStyle.none);

                // Draw name (centered)
                GUIStyle nameStyle = new GUIStyle(EditorStyles.miniLabel);
                nameStyle.alignment = TextAnchor.MiddleCenter;
                GUILayout.Label(vehicle.Prefab.name, nameStyle, GUILayout.Width(80));
                GUILayout.EndVertical();

                // Handle drag & drop (start drag)
                Event evt = Event.current;
                if ((evt.type == EventType.MouseDown || evt.type == EventType.MouseDrag) && thumbRect.Contains(evt.mousePosition))
                {
                    DragAndDrop.PrepareStartDrag();
                    DragAndDrop.objectReferences = new UnityEngine.Object[] { vehicle.Prefab.gameObject };
                    DragAndDrop.StartDrag("Dragging Vehicle Prefab");
                    evt.Use();
                }

                count++;
                if (count % columns == 0)
                {
                    EditorGUILayout.EndHorizontal();
                    EditorGUILayout.BeginHorizontal();
                }
            }

            // Thêm Gate nếu có
            if (vehicleAssetData.Gate != null && vehicleAssetData.Gate.Prefab != null)
            {
                GUILayout.BeginVertical(GUILayout.Width(80));
                GUIContent content = EditorGUIUtility.ObjectContent(vehicleAssetData.Gate.Prefab.gameObject, typeof(GameObject));
                Rect thumbRect = GUILayoutUtility.GetRect(64, 64, GUILayout.ExpandWidth(false), GUILayout.ExpandHeight(false));
                GUI.Box(thumbRect, GUIContent.none);
                GUI.Label(thumbRect, content.image, GUIStyle.none);
                GUIStyle nameStyle = new GUIStyle(EditorStyles.miniLabel);
                nameStyle.alignment = TextAnchor.MiddleCenter;
                GUILayout.Label("Gate", nameStyle, GUILayout.Width(80));
                GUILayout.EndVertical();
                // Handle drag & drop cho Gate
                Event evt = Event.current;
                if ((evt.type == EventType.MouseDown || evt.type == EventType.MouseDrag) && thumbRect.Contains(evt.mousePosition))
                {
                    DragAndDrop.PrepareStartDrag();
                    DragAndDrop.objectReferences = new UnityEngine.Object[] { vehicleAssetData.Gate.Prefab.gameObject };
                    DragAndDrop.StartDrag("Dragging Gate Prefab");
                    evt.Use();
                }
                count++;
                if (count % columns == 0)
                {
                    EditorGUILayout.EndHorizontal();
                    EditorGUILayout.BeginHorizontal();
                }
            }
            EditorGUILayout.EndHorizontal();
            EditorGUILayout.EndVertical();
        }

        private void DrawHumanoidGrid()
        {
            if (selectedLevel == null || selectedLevel.Humanoids == null || selectedLevel.Humanoids.Count == 0)
            {
                EditorGUILayout.HelpBox("No humanoids to display. Please load a level with humanoids.", MessageType.Info);
                return;
            }

            EditorGUILayout.LabelField("Humanoid Grid", EditorStyles.boldLabel);

            int maxLine = selectedLevel.MaxLine;
            if (maxLine <= 0) maxLine = 1;

            int totalHumanoids = selectedLevel.Humanoids.Count;
            int humanoidsPerBlock = maxLine * ROWS_PER_BLOCK;
            int totalBlocks = Mathf.CeilToInt((float)totalHumanoids / humanoidsPerBlock);

            float gridHeight = totalBlocks * (ROWS_PER_BLOCK * (CELL_SIZE + CELL_SPACING) + BLOCK_SPACING);
            humanoidGridScrollPosition = EditorGUILayout.BeginScrollView(humanoidGridScrollPosition, GUILayout.Height(Mathf.Min(gridHeight, 300)));

            // Add a dummy element to avoid the GetLastRect error
            GUILayout.Space(1);

            // Store the grid's position after BeginScrollView
            humanoidGridRect = GUILayoutUtility.GetLastRect();

            cellRects.Clear(); // Clear previous cell rects
            for (int blockIndex = 0; blockIndex < totalBlocks; blockIndex++)
            {
                EditorGUILayout.BeginVertical("box");
                EditorGUILayout.LabelField($"Block {blockIndex + 1}", EditorStyles.boldLabel);

                for (int row = 0; row < ROWS_PER_BLOCK; row++)
                {
                    EditorGUILayout.BeginHorizontal();
                    for (int col = 0; col < maxLine; col++)
                    {
                        int humanoidIndex = blockIndex * humanoidsPerBlock + row * maxLine + col;
                        if (humanoidIndex < totalHumanoids)
                        {
                            // Use fixed size box to ensure consistent cell dimensions
                            GUILayout.Box("", GUILayout.Width(CELL_SIZE), GUILayout.Height(CELL_SIZE));
                            Rect cellRect = GUILayoutUtility.GetLastRect();

                            // Store absolute position adjusted for scroll
                            cellRects[humanoidIndex] = new Rect(
                                cellRect.x + humanoidGridRect.x,
                                cellRect.y + humanoidGridRect.y - humanoidGridScrollPosition.y,
                                cellRect.width,
                                cellRect.height
                            );

                            if (!draggedCellIndices.Contains(humanoidIndex))
                            {
                                DrawHumanoidCell(humanoidIndex, selectedLevel.Humanoids[humanoidIndex], cellRects[humanoidIndex]);
                            }
                        }
                        else
                        {
                            GUILayout.Box("", GUILayout.Width(CELL_SIZE), GUILayout.Height(CELL_SIZE));
                        }
                    }
                    EditorGUILayout.EndHorizontal();
                }
                EditorGUILayout.EndVertical();
                if (blockIndex < totalBlocks - 1)
                {
                    EditorGUILayout.Space(BLOCK_SPACING);
                }
            }

            // Draw dragged cells
            // if (isDragging && draggedCellIndices.Count > 0)
            // {
            //     Vector2 mousePos = Event.current.mousePosition;
            //     int dragIndex = 0;
            //     foreach (int draggedIndex in draggedCellIndices)
            //     {
            //         if (draggedIndex < selectedLevel.Humanoids.Count)
            //         {
            //             // Arrange dragged cells in a stack or grid
            //             Rect dragRect = new Rect(
            //                 mousePos.x + dragOffset.x + (dragIndex % 3) * (CELL_SIZE + CELL_SPACING),
            //                 mousePos.y + dragOffset.y + (dragIndex / 3) * (CELL_SIZE + CELL_SPACING),
            //                 CELL_SIZE,
            //                 CELL_SIZE
            //             );
            //             DrawHumanoidCell(draggedIndex, selectedLevel.Humanoids[draggedIndex], dragRect, true);
            //             dragIndex++;
            //         }
            //     }
            // }

            // Draw target insert highlight
            // if (targetInsertIndex != -1 && cellRects.ContainsKey(targetInsertIndex))
            // {
            //     Rect targetRect = cellRects[targetInsertIndex];
            //     GUI.color = new Color(1f, 1f, 0f, 0.5f);
            //     GUI.DrawTexture(new Rect(targetRect.x - 2, targetRect.y - 2, targetRect.width + 4, targetRect.height + 4), EditorGUIUtility.whiteTexture);
            //     GUI.color = Color.white;
            // }

            EditorGUILayout.EndScrollView();
            DrawHumanoidGridControls();
        }

        private void DrawHumanoidCell(int index, HumanoidData humanoidData, Rect cellRect, bool isDraggingCell = false)
        {
            Color cellColor = VehicleHelper.GetColorFromEColor(humanoidData.color, humanoidData.targetVehicleType);
            bool isSelected = selectedCellIndices.Contains(index);
            bool isBeingDragged = isDraggingCell || draggedCellIndices.Contains(index);

            Color originalColor = GUI.color;

            if (isBeingDragged)
            {
                // For dragged cells, draw with transparency and dashed border
                GUI.color = new Color(cellColor.r, cellColor.g, cellColor.b, 0.7f);
                GUI.DrawTexture(cellRect, EditorGUIUtility.whiteTexture);
                GridHelper.DrawDashedRect(cellRect);
            }
            else
            {
                // Draw color fill
                GUI.color = isSelected ? Color.Lerp(cellColor, Color.white, 0.3f) : cellColor;
                Rect colorRect = new Rect(cellRect.x + 2, cellRect.y + 2, cellRect.width - 4, cellRect.height - 4);
                GUI.DrawTexture(colorRect, EditorGUIUtility.whiteTexture);

                // Draw animation effect for selected cells
                if (dragAnimationTime > 0 && selectedCellIndices.Contains(index))
                {
                    float scale = 1f + 0.1f * Mathf.Sin(dragAnimationTime * Mathf.PI / 0.3f);
                    Rect animRect = new Rect(
                        cellRect.x + cellRect.width * (1 - scale) / 2,
                        cellRect.y + cellRect.height * (1 - scale) / 2,
                        cellRect.width * scale,
                        cellRect.height * scale
                    );
                    GUI.DrawTexture(animRect, EditorGUIUtility.whiteTexture);
                }
            }

            // Draw text
            GUI.color = isSelected || isBeingDragged ? Color.black : Color.white;
            string cellText = HumanoidHelper.GetVehicleTypeText(humanoidData.targetVehicleType);

            GUIStyle textStyle = new GUIStyle(EditorStyles.boldLabel);
            textStyle.alignment = TextAnchor.MiddleCenter;
            textStyle.fontSize = 12;
            if (isSelected || isBeingDragged)
            {
                textStyle.fontStyle = FontStyle.Bold;
            }

            GUI.Label(cellRect, cellText, textStyle);
            GUI.color = originalColor;

            // Handle mouse events
            if (!isBeingDragged)
            {
                HandleCellMouseEvents(index, cellRect);
            }
        }

        private void HandleCellClick(int index)
        {
            Event currentEvent = Event.current;
            bool isCtrl = currentEvent != null && (currentEvent.control || currentEvent.command);
            bool isShift = currentEvent != null && currentEvent.shift;

            if (isCtrl)
            {
                if (selectedCellIndices.Contains(index))
                {
                    selectedCellIndices.Remove(index);
                }
                else
                {
                    selectedCellIndices.Add(index);
                }
                targetHumanoidIndex = -1; // Clear target when using Ctrl
            }
            else if (isShift)
            {
                // Shift + click: select range of humanoids
                if (selectedCellIndices.Count > 0)
                {
                    int lastSelectedIndex = selectedCellIndices[selectedCellIndices.Count - 1];
                    SelectRange(lastSelectedIndex, index);
                }
                else
                {
                    // If no selection, just select this cell
                    selectedCellIndices.Add(index);
                }
                targetHumanoidIndex = -1; // Clear target when using Shift
            }
            else
            {
                // If clicking on an already selected cell, deselect it
                if (selectedCellIndices.Contains(index))
                {
                    selectedCellIndices.Remove(index);
                    targetHumanoidIndex = -1;
                }
                // If exactly 1 cell is selected and clicking on a different cell => swap
                else if (selectedCellIndices.Count == 1 && !selectedCellIndices.Contains(index))
                {
                    int firstIndex = selectedCellIndices[0];
                    SwapHumanoids(firstIndex, index);
                    selectedCellIndices.Clear();
                    targetHumanoidIndex = -1;
                }
                // If waiting for target and clicking on a different cell => execute move action
                else if (waitingForTarget && !selectedCellIndices.Contains(index))
                {
                    targetHumanoidIndex = index;
                    ExecuteMoveAction();
                }
                // If multiple cells are selected and clicking on a different cell => set as target (legacy behavior)
                else if (selectedCellIndices.Count > 1 && !selectedCellIndices.Contains(index))
                {
                    targetHumanoidIndex = index;
                }
                else
                {
                    // Select only this cell
                    selectedCellIndices.Clear();
                    selectedCellIndices.Add(index);
                    targetHumanoidIndex = -1;
                }
            }
            Repaint();
        }

        private void HandleCellMouseEvents(int index, Rect cellRect)
        {
            Event currentEvent = Event.current;

            if (currentEvent.type == EventType.MouseDown && currentEvent.button == 0)
            {
                if (cellRect.Contains(currentEvent.mousePosition))
                {
                    HandleCellClick(index);
                    dragStartPosition = currentEvent.mousePosition;
                    dragOffset = Vector2.zero; // Reset offset to align with mouse
                    currentEvent.Use();
                }
            }
            // else if (currentEvent.type == EventType.MouseDrag)
            // {
            //     if (!isDragging && Vector2.Distance(currentEvent.mousePosition, dragStartPosition) > 5f)
            //     {
            //         isDragging = true;
            //         draggedCellIndices.Clear();
            //         if (selectedCellIndices.Contains(index))
            //         {
            //             draggedCellIndices.AddRange(selectedCellIndices);
            //         }
            //         else
            //         {
            //             draggedCellIndices.Add(index);
            //         }
            //         Repaint();
            //     }

            //     if (isDragging)
            //     {
            //         targetInsertIndex = GetCellIndexAtPosition(currentEvent.mousePosition);
            //         // Clear target humanoid when dragging
            //         targetHumanoidIndex = -1;
            //         waitingForTarget = false;
            //         waitingForTargetAction = "";
            //         currentEvent.Use();
            //         Repaint();
            //     }
            // }
            // else if (currentEvent.type == EventType.MouseUp && isDragging)
            // {
            //     Vector2 mousePosition = currentEvent.mousePosition;
            //     int targetIndex = GetCellIndexAtPosition(mousePosition);

            //     if (targetIndex != -1 && !draggedCellIndices.Contains(targetIndex))
            //     {
            //         MoveHumanoidsToPosition(draggedCellIndices, targetIndex);
            //         selectedCellIndices.Clear(); // Clear selection after drop
            //     }

            //     isDragging = false;
            //     draggedCellIndices.Clear();
            //     targetInsertIndex = -1;
            //     mouseUpHandled = true;
            //     dragAnimationTime = 0.3f;
            //     currentEvent.Use();
            //     Repaint();
            // }
        }

        private void SwapHumanoids(int index1, int index2)
        {
            if (selectedLevel == null || selectedLevel.Humanoids == null ||
                index1 < 0 || index2 < 0 ||
                index1 >= selectedLevel.Humanoids.Count || index2 >= selectedLevel.Humanoids.Count)
            {
                return;
            }

            var temp = selectedLevel.Humanoids[index1];
            selectedLevel.Humanoids[index1] = selectedLevel.Humanoids[index2];
            selectedLevel.Humanoids[index2] = temp;
            EditorUtility.SetDirty(selectedLevel);
            Debug.Log($"Swapped humanoids at indices {index1} and {index2}");
        }

        private void MoveHumanoidsToPosition(List<int> sourceIndices, int targetIndex)
        {
            if (selectedLevel == null || selectedLevel.Humanoids == null ||
                sourceIndices == null || sourceIndices.Count == 0 ||
                targetIndex < 0 || targetIndex >= selectedLevel.Humanoids.Count)
            {
                return;
            }

            List<int> sortedSourceIndices = new List<int>(sourceIndices);
            sortedSourceIndices.Sort();
            List<HumanoidData> humanoidsToMove = new List<HumanoidData>();
            foreach (int index in sortedSourceIndices)
            {
                if (index >= 0 && index < selectedLevel.Humanoids.Count)
                {
                    humanoidsToMove.Add(selectedLevel.Humanoids[index]);
                }
            }

            List<HumanoidData> newHumanoidsList = new List<HumanoidData>();
            for (int i = 0; i < selectedLevel.Humanoids.Count; i++)
            {
                if (!sortedSourceIndices.Contains(i))
                {
                    newHumanoidsList.Add(selectedLevel.Humanoids[i]);
                }
            }

            int adjustedTargetIndex = targetIndex;
            for (int i = 0; i < sortedSourceIndices.Count; i++)
            {
                if (sortedSourceIndices[i] < targetIndex)
                {
                    adjustedTargetIndex--;
                }
            }

            adjustedTargetIndex = Mathf.Max(0, Mathf.Min(adjustedTargetIndex, newHumanoidsList.Count));
            for (int i = 0; i < humanoidsToMove.Count; i++)
            {
                int insertIndex = adjustedTargetIndex + i;
                newHumanoidsList.Insert(insertIndex, humanoidsToMove[i]);
            }

            selectedLevel.Humanoids.Clear();
            selectedLevel.Humanoids.AddRange(newHumanoidsList);
            EditorUtility.SetDirty(selectedLevel);
            Repaint();
        }

        private void DrawHumanoidGridControls()
        {
            EditorGUILayout.BeginHorizontal();
            GUI.backgroundColor = Color.orange;
            if (GUILayout.Button("Reset Changes", GUILayout.Height(30)))
            {
                ResetHumanoidChanges();
            }
            GUI.backgroundColor = Color.green;
            if (GUILayout.Button("Save Changes", GUILayout.Height(30)))
            {
                SaveHumanoidChanges();
            }
            GUI.backgroundColor = Color.white;

            EditorGUILayout.EndHorizontal();

            // Show move controls when humanoids are selected
            if (selectedCellIndices.Count > 0)
            {

                EditorGUILayout.Space();
                EditorGUILayout.LabelField("Move Controls", EditorStyles.boldLabel);
                EditorGUILayout.BeginHorizontal();

                if (waitingForTarget)
                {
                    // Show waiting state
                    GUI.enabled = false;
                    GUILayout.Button("Move Before Target", GUILayout.Width(120));
                    GUILayout.Button("Move After Target", GUILayout.Width(120));
                    GUI.enabled = true;

                    EditorGUILayout.LabelField($"Waiting for target to move {waitingForTargetAction}...", EditorStyles.miniLabel);

                    if (GUILayout.Button("Cancel", GUILayout.Width(60)))
                    {
                        waitingForTarget = false;
                        waitingForTargetAction = "";
                        targetHumanoidIndex = -1;
                        Repaint();
                    }
                }
                else
                {
                    // Show normal state
                    if (GUILayout.Button("Move Before Target", GUILayout.Width(120)))
                    {
                        MoveSelectedHumanoidsBeforeTarget();
                    }
                    if (GUILayout.Button("Move After Target", GUILayout.Width(120)))
                    {
                        MoveSelectedHumanoidsAfterTarget();
                    }

                    EditorGUILayout.LabelField("Click button, then click target humanoid", EditorStyles.miniLabel);
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.HelpBox($"Selected {selectedCellIndices.Count} humanoid(s).\n\nControls:\n• Ctrl + Click: Multi-select individual humanoids\n• Shift + Click: Select range of humanoids\n• Click move button, then click target humanoid", MessageType.Info);
            }
        }

        private void MoveSelectedHumanoidsBeforeTarget()
        {
            if (selectedCellIndices.Count == 0)
                return;

            waitingForTarget = true;
            waitingForTargetAction = "before";
            targetHumanoidIndex = -1;
            Repaint();
        }

        private void MoveSelectedHumanoidsAfterTarget()
        {
            if (selectedCellIndices.Count == 0)
                return;

            waitingForTarget = true;
            waitingForTargetAction = "after";
            targetHumanoidIndex = -1;
            Repaint();
        }

        private void ExecuteMoveAction()
        {
            if (!waitingForTarget || targetHumanoidIndex == -1 || selectedCellIndices.Count == 0)
                return;

            if (waitingForTargetAction == "before")
            {
                MoveHumanoidsToPosition(selectedCellIndices, targetHumanoidIndex);
            }
            else if (waitingForTargetAction == "after")
            {
                MoveHumanoidsToPosition(selectedCellIndices, targetHumanoidIndex + 1);
            }

            // Reset state
            selectedCellIndices.Clear();
            targetHumanoidIndex = -1;
            waitingForTarget = false;
            waitingForTargetAction = "";
            Repaint();
        }

        private void SelectRange(int startIndex, int endIndex)
        {
            if (selectedLevel == null || selectedLevel.Humanoids == null)
                return;

            // Ensure startIndex is smaller than endIndex
            int minIndex = Mathf.Min(startIndex, endIndex);
            int maxIndex = Mathf.Max(startIndex, endIndex);

            // Clear current selection and add all indices in range
            selectedCellIndices.Clear();
            for (int i = minIndex; i <= maxIndex; i++)
            {
                if (i >= 0 && i < selectedLevel.Humanoids.Count)
                {
                    selectedCellIndices.Add(i);
                }
            }
        }

        private void RandomizeHumanoids()
        {
            if (selectedLevel == null || selectedLevel.Humanoids == null)
                return;

            for (int i = selectedLevel.Humanoids.Count - 1; i > 0; i--)
            {
                int randomIndex = UnityEngine.Random.Range(0, i + 1);
                var temp = selectedLevel.Humanoids[i];
                selectedLevel.Humanoids[i] = selectedLevel.Humanoids[randomIndex];
                selectedLevel.Humanoids[randomIndex] = temp;
            }

            EditorUtility.SetDirty(selectedLevel);
            Repaint();
        }

        private void SaveHumanoidChanges()
        {
            if (selectedLevel != null)
            {
                EditorUtility.SetDirty(selectedLevel);
                AssetDatabase.SaveAssets();
                EditorUtility.DisplayDialog("Success", "Humanoid changes saved!", "OK");
            }
        }

        private void ResetHumanoidChanges()
        {
            if (selectedLevel != null)
            {
                EditorUtility.ClearDirty(selectedLevel);
                AssetDatabase.Refresh();
            }
        }

        private void RandomizeVehicleColors()
        {
            if (selectedLevel == null)
            {
                EditorUtility.DisplayDialog("Error", "No level selected. Please select a level first.", "OK");
                return;
            }

            if (selectedLevel.Vehicles == null || selectedLevel.Vehicles.Count == 0)
            {
                EditorUtility.DisplayDialog("Info", "No vehicles found in the selected level.", "OK");
                return;
            }

            // Get all values from EColor enum
            EColor[] availableColors = (EColor[])System.Enum.GetValues(typeof(EColor));
            if (availableColors == null || availableColors.Length == 0)
            {
                EditorUtility.DisplayDialog("Error", "No colors available for randomization.", "OK");
                return;
            }

            var container = GetContainer();
            var vehicleComponents = container.GetComponentsInChildren<Vehicle>();
            foreach (var vehicle in vehicleComponents)
            {
                int randomColorIndex = UnityEngine.Random.Range(0, availableColors.Length);
                var color = availableColors[randomColorIndex];
                vehicle.SetColor(color, colorManager);
            }

            Repaint();
        }

        private void GenerateHumanoids()
        {
            if (selectedLevel == null)
            {
                EditorUtility.DisplayDialog("Error", "No level selected. Please select a level first.", "OK");
                return;
            }

            if (selectedLevel.Vehicles == null || selectedLevel.Vehicles.Count == 0)
            {
                EditorUtility.DisplayDialog("Error", "No vehicles found in the level. Please add vehicles first.", "OK");
                return;
            }

            selectedLevel.Humanoids.Clear();
            GenerateHumanoidsForVehicles();
            GenerateHumanoidsForGarages();
            // UpdateMaxLine();

            EditorUtility.SetDirty(selectedLevel);
            AssetDatabase.SaveAssets();
            Debug.Log($"Generated {selectedLevel.Humanoids.Count} humanoids for level: {selectedLevel.name}");
        }

        private void GenerateHumanoidsForVehicles()
        {
            foreach (var vehicleData in selectedLevel.Vehicles)
            {
                int humanoidCount = vehicleData.Length;
                for (int i = 0; i < humanoidCount; i++)
                {
                    var humanoidData = new HumanoidData
                    {
                        color = vehicleData.color,
                        targetVehicleType = vehicleData.vehicleType == EVehicleType.MysteryVehicle ?
                            EVehicleType.NormalVehicle : vehicleData.vehicleType
                    };
                    selectedLevel.Humanoids.Add(humanoidData);
                }
            }
        }

        private void GenerateHumanoidsForGarages()
        {
            Debug.Log($"Generating humanoids for garages... {selectedLevel.Garages.Count} garages found.");
            if (selectedLevel.Garages == null || selectedLevel.Garages.Count == 0)
                return;

            foreach (var garageData in selectedLevel.Garages)
            {
                int vehicleLength = (int)garageData.VehicleSize.y;
                if (garageData.VehicleColors != null)
                {
                    foreach (var vehicleColor in garageData.VehicleColors)
                    {
                        for (int i = 0; i < vehicleLength; i++)
                        {
                            var humanoidData = new HumanoidData
                            {
                                color = vehicleColor,
                                targetVehicleType = EVehicleType.NormalVehicle
                            };
                            selectedLevel.Humanoids.Add(humanoidData);
                        }
                    }
                }
            }
        }

        private void ChangeVehicleColor(EColor newColor)
        {
            if (selectedVehicle == null || colorManager == null) return;
            selectedVehicle.SetColor(newColor, colorManager);
            editingVehicleColor = newColor;
        }

        private void ApplyGateVehicleSizeChange()
        {
            if (selectedGate == null) return;

            // Update the garage data in the gate directly
            var garageData = selectedGate.GetGarageData();
            if (garageData != null)
            {
                garageData.VehicleSize = editingGateVehicleSize;
                selectedGate.SetData(garageData); // Update the gate with new data
                Debug.Log($"Updated gate vehicle size: {editingGateVehicleSize}");
            }
        }

        private void AddRandomColorsToGate()
        {
            if (selectedGate == null) return;

            var garageData = selectedGate.GetGarageData();
            if (garageData?.VehicleColors == null) return;

            EColor[] allColors = (EColor[])System.Enum.GetValues(typeof(EColor));
            var vehicleColors = garageData.VehicleColors;

            // Add 3-5 random colors (can be duplicates)
            int colorsToAdd = UnityEngine.Random.Range(3, 6);
            for (int i = 0; i < colorsToAdd; i++)
            {
                int randomIndex = UnityEngine.Random.Range(0, allColors.Length);
                vehicleColors.Add(allColors[randomIndex]);
            }

            Debug.Log($"Added {colorsToAdd} random colors to gate. Total colors: {vehicleColors.Count}");
        }

        private void ValidateHumanoidData()
        {
            if (selectedLevel == null) return;

            List<string> warnings = new List<string>();
            List<string> errors = new List<string>();

            // Get expected humanoid count from vehicles and garages
            int expectedHumanoidCount = HumanoidHelper.CalculateExpectedHumanoidCount(selectedLevel);
            int actualHumanoidCount = selectedLevel.Humanoids?.Count ?? 0;

            // Check humanoid count mismatch
            if (expectedHumanoidCount != actualHumanoidCount)
            {
                if (actualHumanoidCount == 0)
                {
                    errors.Add($"No humanoids found. Expected {expectedHumanoidCount} humanoids based on vehicles and garages.");
                }
                else if (actualHumanoidCount < expectedHumanoidCount)
                {
                    warnings.Add($"Humanoid count mismatch: Found {actualHumanoidCount}, expected {expectedHumanoidCount}. {expectedHumanoidCount - actualHumanoidCount} humanoids missing.");
                }
                else
                {
                    warnings.Add($"Humanoid count mismatch: Found {actualHumanoidCount}, expected {expectedHumanoidCount}. {actualHumanoidCount - expectedHumanoidCount} extra humanoids.");
                }
            }

            // Validate color distribution
            HumanoidHelper.ValidateHumanoidColorDistribution(warnings, selectedLevel);

            // Validate vehicle type distribution
            ValidateHumanoidVehicleTypeDistribution(warnings);

            // Display validation results
            DisplayValidationResults(warnings, errors);
        }

        private void ValidateHumanoidVehicleTypeDistribution(List<string> warnings)
        {
            if (selectedLevel.Humanoids == null || selectedLevel.Humanoids.Count == 0) return;

            // Count expected vehicle types from vehicles
            Dictionary<EVehicleType, int> expectedTypes = new Dictionary<EVehicleType, int>();
            Dictionary<EVehicleType, int> actualTypes = new Dictionary<EVehicleType, int>();

            if (selectedLevel.Vehicles != null)
            {
                foreach (var vehicle in selectedLevel.Vehicles)
                {
                    EVehicleType targetType = vehicle.vehicleType == EVehicleType.MysteryVehicle ?
                        EVehicleType.NormalVehicle : vehicle.vehicleType;

                    if (!expectedTypes.ContainsKey(targetType))
                        expectedTypes[targetType] = 0;
                    expectedTypes[targetType] += vehicle.Length;
                }
            }

            // Garages always spawn NormalVehicle type
            if (selectedLevel.Garages != null)
            {
                foreach (var garage in selectedLevel.Garages)
                {
                    int vehicleLength = (int)garage.VehicleSize.y;
                    int colorCount = garage.VehicleColors?.Count ?? 0;

                    if (!expectedTypes.ContainsKey(EVehicleType.NormalVehicle))
                        expectedTypes[EVehicleType.NormalVehicle] = 0;
                    expectedTypes[EVehicleType.NormalVehicle] += vehicleLength * colorCount;
                }
            }

            // Count actual vehicle types from humanoids
            foreach (var humanoid in selectedLevel.Humanoids)
            {
                if (!actualTypes.ContainsKey(humanoid.targetVehicleType))
                    actualTypes[humanoid.targetVehicleType] = 0;
                actualTypes[humanoid.targetVehicleType]++;
            }

            // Compare expected vs actual types
            foreach (var expectedType in expectedTypes)
            {
                int actualCount = actualTypes.ContainsKey(expectedType.Key) ? actualTypes[expectedType.Key] : 0;
                if (actualCount != expectedType.Value)
                {
                    warnings.Add($"Vehicle Type {expectedType.Key}: Expected {expectedType.Value} humanoids, found {actualCount}");
                }
            }
        }

        private void DisplayValidationResults(List<string> warnings, List<string> errors)
        {
            // Display errors first
            if (errors.Count > 0)
            {
                EditorGUILayout.Space();
                foreach (string error in errors)
                {
                    EditorGUILayout.HelpBox($"❌ ERROR: {error}", MessageType.Error);
                }
            }

            // Display warnings
            if (warnings.Count > 0)
            {
                EditorGUILayout.Space();
                foreach (string warning in warnings)
                {
                    EditorGUILayout.HelpBox($"⚠️ WARNING: {warning}", MessageType.Warning);
                }

                // Add fix button for common issues
                EditorGUILayout.Space();
                EditorGUILayout.BeginHorizontal();
                GUI.backgroundColor = Color.yellow;
                if (GUILayout.Button("🔧 Auto-Fix Humanoids", GUILayout.Height(25)))
                {
                    if (EditorUtility.DisplayDialog("Auto-Fix Humanoids",
                        "This will regenerate humanoids to match vehicles and garages. Continue?",
                        "Fix", "Cancel"))
                    {
                        HumanoidHelper.FixHumanoidData(selectedLevel);
                    }
                }
                GUI.backgroundColor = Color.white;
                EditorGUILayout.EndHorizontal();
            }
            else if (errors.Count == 0)
            {
                // Display success message when everything is valid
                EditorGUILayout.Space();
                EditorGUILayout.HelpBox("✅ All humanoid data is valid and consistent!", MessageType.Info);
            }
        }

        public Transform GetContainer()
        {
            var levelLoader = FindFirstObjectByType<LevelLoader>();
            if (levelLoader == null)
            {
                EditorUtility.DisplayDialog("Error", "No LevelLoader found in the scene. Please add a LevelLoader component to the scene.", "OK");
                return null;
            }
            return levelLoader.transform;
        }

        private void CreateButton(string title, Action action, Color color, params GUILayoutOption[] options)
        {
            GUI.backgroundColor = color;
            if (GUILayout.Button(title, GUILayout.Height(30)))
            {
                action.Invoke();
            }
            GUI.backgroundColor = Color.white;
        }

        private void RemoveSelectedHumanoids()
        {
            for (int i = selectedLevel.Humanoids.Count - 1; i >= 0; i--)
            {
                if (selectedCellIndices.Contains(i))
                {
                    selectedLevel.Humanoids.RemoveAt(i);
                }
            }

            EditorUtility.SetDirty(selectedLevel);
        }

        private void DrawColorChangeSection()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField($"🎨 Change Color for Selected Humanoids ({selectedCellIndices.Count})", EditorStyles.boldLabel);

            EditorGUILayout.BeginHorizontal();
            foreach (EColor color in Enum.GetValues(typeof(EColor)))
            {
                CreateButton(color.ToString().ToUpper(), () => ApplyHumanoidColor(color), VehicleHelper.GetColorFromEColor(color));
            }

            CreateButton("VIP", () => ApplyHumanoidType(EVehicleType.Limousine), Color.black);
            CreateButton("FIRE", () => ApplyHumanoidType(EVehicleType.FireTruck), Color.softRed);

            GUI.backgroundColor = Color.white;

            EditorGUILayout.EndHorizontal();
            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(5);
        }

        void ApplyHumanoidColor(EColor color)
        {
            foreach (int index in selectedCellIndices)
            {
                var humanoid = selectedLevel.Humanoids[index];
                humanoid.color = color;
                humanoid.targetVehicleType = EVehicleType.NormalVehicle;
                selectedLevel.Humanoids[index] = humanoid;
            }

            EditorUtility.SetDirty(selectedLevel);
        }

        void ApplyHumanoidType(EVehicleType vehicleType)
        {
            foreach (int index in selectedCellIndices)
            {
                var humanoid = selectedLevel.Humanoids[index];
                humanoid.targetVehicleType = vehicleType;
                selectedLevel.Humanoids[index] = humanoid;
            }

            EditorUtility.SetDirty(selectedLevel);
        }
    }
}
