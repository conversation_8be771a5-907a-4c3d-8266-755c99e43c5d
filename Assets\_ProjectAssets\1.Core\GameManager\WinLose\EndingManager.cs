using DG.Tweening;
using DSDK.Core;
using DSDK.Data;
using DSDK.UISystem;
using UnityEngine;

public class EndingManager : MonoBehaviour
{
    Tween delayLoseTween;
    private void OnEnable()
    {
        MonoBehaviourEventExtensions.AddEventListener(this, EventID.OnWin, OnWinHandler);
        MonoBehaviourEventExtensions.AddEventListener(this, EventID.OnLose, OnLoseHandler);
    }
    private void OnDisable()
    {
        MonoBehaviourEventExtensions.RemoveEventListener(this, EventID.OnWin, OnWinHandler);
        MonoBehaviourEventExtensions.RemoveEventListener(this, EventID.OnLose, OnLoseHandler);
    }

    public void OnWinHandler()
    {
        GameManager.Instance.SetGameState(EGameState.Paused);

        AdWrapper.Instance.ShowInterstitial((isSuccess) =>
        {
            UISystem.Instance.ShowPanel<WinPopup>();
        }, Location.GameEnd);
    }

    public void OnLoseHandler()
    {
        GameManager.Instance.SetGameState(EGameState.Paused);

        if (GameManager.Instance.ParkingSlotsManager.HaveLockedSlot())
        {
            UISystem.Instance.ShowPanel<OutOfSpacePopup>();
        }
        else
        {
            AdWrapper.Instance.ShowInterstitial((isSuccess) =>
            {
                UISystem.Instance.ShowPanel<LosePopup>();
            }, Location.GameEnd);
        }

    }
}
