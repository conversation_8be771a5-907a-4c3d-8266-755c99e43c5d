using System;
using DSDK.Core;
using DSDK.Data;
using DSDK.UISystem;
using Moonee.Moon_SDK.Internal.Advertisement;
using UnityEngine;

public class AdWrapper : SingletonMonoBehaviour<AdWrapper>
{
    [SerializeField] private int levelToShowInterstitial = 5;
    [SerializeField] private float interstitialTimer = 30;

    private float _lastInterstitialTime = -1800;
    private int levelCount = 0;

    public bool IsRewardedVideoReady()
    {
        return AdvertisementManager.IsRewardedAdReady();
    }

    public void ShowRewardedVideo(Action<bool> callback, string name = "", int level = 0)
    {
        AdvertisementManager.ShowRewardedAd(() =>
        {
            Debug.Log("Rewarded video started");
        }, () =>
        {
            Debug.Log("Rewarded video done");
        }, () =>
        {
            GameManager.Instance.ShowErrorToast("Ads not available");
            callback?.Invoke(false);
        }, () =>
        {
            callback?.Invoke(true);
        }, level, name);
    }

    public bool CanShowInterstitial()
    {
        return !MoonSDKManager.I.IsD0 && Time.time - _lastInterstitialTime >= interstitialTimer && levelCount <= 0;
    }

    public void ShowInterstitial(Action<bool> onFinished, Location location = Location.None)
    {
        if (location == Location.GameEnd)
        {
            levelCount--;
        }
        if (!CanShowInterstitial())
        {
            // GameManager.Instance.ShowErrorToast("Ads not available");
            onFinished?.Invoke(false);
            return;
        }

        Debug.Log("ShowInterstitial");
        levelCount = levelToShowInterstitial;
        _lastInterstitialTime = Time.time;
        AdvertisementManager.ShowInterstitial(
            OnStartAdEvent: () =>
            {
                Debug.Log("Interstitial video started");
            },
            OnFinishAdEvent: () =>
            {
                Debug.Log("Interstitial video done");
                if (GameData.I.IsFirstTimeShowInterstitial)
                {
                    HomeScreenManager.IsShowOffer = true;
                    HomeScreenManager.ShowOfferType = HomeScreenManager.OfferType.FirstTimeShowInterstitial;
                }
                onFinished?.Invoke(true);

            },
            OnFailedAdEvent: () =>
            {
                Debug.Log("Interstitial video failed");
                onFinished?.Invoke(false);
            });
    }

    public void ShowBanner()
    {
        if (!CanShowBanner())
        {
            return;
        }
        AdvertisementManager.ShowBanner();
    }

    public void HideBanner()
    {
        AdvertisementManager.HideBanner();
    }

    private bool CanShowBanner()
    {
        return RemoteConfigWrapper.I.GetBannerConfig().CanShow;
    }
}
