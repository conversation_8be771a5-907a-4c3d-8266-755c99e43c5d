using DSDK.Core;
using DSDK.Data;
using DSDK.UISystem;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class RefillHeartPopup : UIPanel
{
    [SerializeField] private Image[] heartImages;
    [SerializeField] private TextMeshProUGUI timeCD;
    [SerializeField] private BaseBtn closeBtn;
    [SerializeField] private BaseBtn pay1Btn;
    [SerializeField] private BaseBtn payAllBtn;
    [SerializeField] private BaseBtn reward1HeartBtn;

    bool isForceLoadScene = false;

    void Start()
    {
        closeBtn.OnClick(OnCloseClickBtn);
        pay1Btn.OnClick(OnPay1Btn);
        payAllBtn.OnClick(OnPayAllBtn);
        reward1HeartBtn.OnClick(OnReward1HeartBtn);


    }

    void Update()
    {
        if (GameData.Instance.remainingTime > 0)
        {
            timeCD.text = Util.ConvertTime2((int)GameData.Instance.remainingTime);
            UpdateHeart();
        }
        else
        {

            timeCD.text = "Full";
        }
    }

    private void UpdateHeart()
    {
        for (int i = 0; i < heartImages.Length; i++)
        {
            heartImages[i].gameObject.SetActive(i < GameData.Instance.CurrentHeart);
        }
        pay1Btn.CanInvokeAction = GameData.Instance.CurrentHeart < 5;
        payAllBtn.CanInvokeAction = GameData.Instance.CurrentHeart < 5;
        reward1HeartBtn.CanInvokeAction = GameData.Instance.CurrentHeart < 5;

        if (GameData.Instance.CurrentCoin < 180)
        {
            pay1Btn.gameObject.SetActive(false);
            payAllBtn.gameObject.SetActive(false);
            reward1HeartBtn.gameObject.SetActive(true);
        }
        else if (GameData.Instance.CurrentCoin < 900)
        {
            pay1Btn.gameObject.SetActive(true);
            payAllBtn.gameObject.SetActive(false);
            reward1HeartBtn.gameObject.SetActive(true);
        }
        else
        {
            pay1Btn.gameObject.SetActive(true);
            payAllBtn.gameObject.SetActive(true);
            reward1HeartBtn.gameObject.SetActive(false);
        }

    }
    public void SetForceLoadScene(bool isForceLoadScene)
    {
        this.isForceLoadScene = isForceLoadScene;
    }
    private void OnCloseClickBtn()
    {
        if (isForceLoadScene)
        {
            if (GameData.Instance.CurrentHeart > 0)
            {
                GameManager.Instance.RestartLevel(true);
            }
            else
            {
                GameManager.Instance.BackToHomeScreen();
            }
        }
        else
        {
            Hide();
        }

    }

    private void OnPay1Btn()
    {
        if (GameData.Instance.CurrentCoin < 180)
        {
            return;
        }
        GameData.Instance.CurrentCoin -= 180;
        GameData.Instance.CurrentHeart++;
        this.DispatchEvent(EventID.OnHeartCountChange);
        this.DispatchEvent(EventID.OnCoinChange);

        UpdateHeart();
    }

    private void OnPayAllBtn()
    {
        if (GameData.Instance.CurrentCoin < 900)
        {

            return;
        }
        GameData.Instance.CurrentCoin -= 900;
        GameData.Instance.CurrentHeart += 5;
        this.DispatchEvent(EventID.OnHeartCountChange);
        this.DispatchEvent(EventID.OnCoinChange);

        UpdateHeart();
    }

    private void OnReward1HeartBtn()
    {
        AdWrapper.Instance.ShowRewardedVideo((isSuccess) =>
        {
            if (isSuccess)
            {
                GameData.Instance.CurrentHeart++;
                this.DispatchEvent(EventID.OnHeartCountChange);

                UpdateHeart();
            }
        }, "refill_OneHeart", GameData.I.CurrentLevelIndex);
    }
}

