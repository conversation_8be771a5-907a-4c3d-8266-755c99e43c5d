using TMPro;
using UnityEngine;

public class IAPPackMultipleItem : IAPPack
{
    [SerializeField] private TextMeshProUGUI namePackText;

    [SerializeField] private IAPPackItem[] iapPackItems;
    public override void InitData()
    {
        base.InitData();
        namePackText.text = iapPackData.namePack;
        for (int i = 0; i < iapPackData.iapItemDatas.Length; i++)
        {
            iapPackItems[i].InitData(iapPackData.iapItemDatas[i]);
        }
    }
}
