using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using DSDK.Extensions;
using System.Linq;
using DSDK.Core;
using DSDK.Audio;
using System;
using System.Collections;
using DSDK.Vibration;


[SelectionBase]
public class Vehicle : GridObject, IPoolable
{
    [SerializeField] private BoxCollider boxCollider;
    [SerializeField] private Animator anim;
    [SerializeField] private MeshRenderer[] vehicleMeshRenderers;
    [SerializeField] private EColor vehicleColor;
    [SerializeField] private EVehicleType vehicleType;
    [SerializeField] private Direction vehicleDirection;
    [SerializeField] private GameObject seatHolder;
    [SerializeField] private GameObject questionMark;
    [SerializeField] private GameObject arrowMark;
    [SerializeField] private GameObject smokeTrail;
    public Transform[] seats;
    public List<HumanoidBlock> sittingHumanoids = new List<HumanoidBlock>();
    private ParkingSlot targetParkingSlot;
    public ParkingSlot TargetParkingSlot => targetParkingSlot;
    private bool isMoving;
    public bool IsMoving => isMoving;
    private bool wasParked = false;
    public bool WasParked => wasParked;
    private Vector3 originalPosition;
    private int vehicleId;
    public bool HasEmptySeat => sittingHumanoids.Count < seats.Length;
    public int CountEmptySeat => seats.Length - sittingHumanoids.Count;
    public EColor Color => vehicleColor;
    public EVehicleType VehicleType => vehicleType;
    public Direction VehicleDirection => vehicleDirection;
    private Vehicle blockedByVehicle;

    public int SlotCount => Length;

    private GridManager gridManager;

    int currentIndexSeat = 0;

    void OnDisable()
    {
        this.RemoveEventListener<EventID, Vehicle>(EventID.OnVehicleCanMove, RevealMysteryVehicle);
    }

    public void Load(VehicleData vehicleData)
    {
        vehicleType = vehicleData.vehicleType;
        vehicleId = GetInstanceID();

        anim.enabled = false;

        SetPosition(vehicleData);
        //  SetDirection(vehicleData.vehicleDirection);
        transform.rotation = Quaternion.Euler(0, vehicleData.AngleY, 0);
        originalPosition = transform.position;

        currentIndexSeat = 0;
        vehicleMeshRenderers[1].gameObject.SetActive(true);
        smokeTrail.SetActive(false);
        seatHolder.SetActive(true);
        if (vehicleType == EVehicleType.MysteryVehicle || vehicleType == EVehicleType.NormalVehicle)
        {
            questionMark.SetActive(vehicleType == EVehicleType.MysteryVehicle);
            arrowMark.SetActive(vehicleType != EVehicleType.MysteryVehicle);
        }
        else
        {
            // questionMark.SetActive(false);
            // arrowMark.SetActive(false);
        }

        if (vehicleType == EVehicleType.NormalVehicle || vehicleType == EVehicleType.MysteryVehicle)
        {
            foreach (var meshRenderer in vehicleMeshRenderers)
            {
                SetMat(meshRenderer);
            }
        }

        if (vehicleType == EVehicleType.MysteryVehicle)
        {
            CancelInvoke(nameof(DelayCheckBlockedVehicle));
            Invoke(nameof(DelayCheckBlockedVehicle), 0.5f);
        }

        UpdateGridOccupation();

    }

    void DelayCheckBlockedVehicle()
    {
        this.AddEventListener<EventID, Vehicle>(EventID.OnVehicleCanMove, RevealMysteryVehicle);
        blockedByVehicle = GetBlockedByVehicle();
        Debug.Log("getting blocked by vehicle");
    }

    private void SetPosition(VehicleData vehicleData)
    {
        Width = vehicleData.Width;
        Length = vehicleData.Length;
        Top = vehicleData.Top;
        Left = vehicleData.Left;
        vehicleColor = vehicleData.color;
        // SetPosition(vehicleData.Top, vehicleData.Left, vehicleData.vehicleDirection, gridManager);
        transform.localPosition = vehicleData.Position;
    }

    public void SetColor(EColor color, ColorManager colorManager)
    {
        if (VehicleType != EVehicleType.NormalVehicle)
        {
            return;
        }
        vehicleColor = color;
        var colorMat = colorManager.GetColor(color);
        SetMaterial(colorMat);
    }

    public void SetData(VehicleData vehicleData)
    {
        Width = vehicleData.Width;
        Length = vehicleData.Length;
        Top = vehicleData.Top;
        Left = vehicleData.Left;
        vehicleColor = vehicleData.color;
        vehicleType = vehicleData.vehicleType;
        vehicleDirection = vehicleData.vehicleDirection;
    }


    public VehicleData GetData()
    {
        return new VehicleData
        {
            Width = Width,
            Length = Length,
            Top = Top,
            Left = Left,
            color = vehicleColor,
            vehicleDirection = vehicleDirection,
            Position = transform.localPosition,
            AngleY = transform.localEulerAngles.y,
            vehicleType = vehicleType
        };
    }

    private void SetMat(MeshRenderer meshRenderer)
    {
        Material[] tempMat;
#if UNITY_EDITOR
        if (!Application.isPlaying)
        {
            tempMat = meshRenderer.sharedMaterials;
            tempMat[0] = GameManager.Instance.LevelManager.ColorManager.GetColor(vehicleColor);
            meshRenderer.sharedMaterials = tempMat;
            return;
        }
#endif
        tempMat = meshRenderer.materials;
        if (vehicleType == EVehicleType.MysteryVehicle)
        {
            tempMat[0] = GameManager.Instance.LevelManager.MysteryMaterial;
        }
        else
        {
            tempMat[0] = GameManager.Instance.LevelManager.ColorManager.GetColor(vehicleColor);

        }
        meshRenderer.materials = tempMat;
    }

    public void HandleTouchInput()
    {
        Debug.Log($"HandleTouchInput {isMoving} {GameManager.Instance.CurrentGameState}");
        VibrationManager.Instance.Vibrate(HapticType.Large, 250);
        if (isMoving || anim.enabled)
        {
            return;
        }
        if (vehicleType == EVehicleType.MysteryVehicle)
        {
            return;
        }

        if (GameManager.Instance.CurrentGameState == EGameState.Waiting || GameManager.Instance.CurrentGameState == EGameState.Tutorial)
        {
            this.DispatchEvent(EventID.OnVehicleSelected, this);
            return;
        }

        MoveVehicleToTargetParkingSlot();
    }

    public void MoveVehicleToTargetParkingSlot()
    {
        Vector3[] path;
        targetParkingSlot = GameManager.Instance.FindAvailableSlot(transform, out path);
        if (targetParkingSlot == null)
        {
            // GameManager.Instance.ShowErrorToast("<sprite name=\"icon_warning_toast\"> No slot available");
            return;
        }

        isMoving = true;
        smokeTrail.SetActive(true);
        originalPosition = transform.position;

        // Check for blocking using direct forward raycast
        Vector3? blockedPosition = GetBlockedPosition();
        bool canContinueMoving = !blockedPosition.HasValue;
        anim.enabled = false;
        if (canContinueMoving)
        {
            targetParkingSlot.ParkVehicle(this);
            GameManager.Instance.ParkingSlotsManager.CheckAvailableSlot();

            // if (gridManager != null)
            // {
            //     gridManager.ClearObjectOccupation(vehicleId);
            // }
            AudioServer.I.Shot(eAudio.SFX_Vehicle_Start.ToString());
            this.DispatchEvent(EventID.OnVehicleCanMove, this);
            transform.DOPath(path, GameManager.Instance.GameConfigManager.vehicleConfig.moveSpeed, PathType.CatmullRom)
                .SetLookAt(0.01f)
                .SetEase(GameManager.Instance.GameConfigManager.vehicleConfig.moveCurve)
                .OnWaypointChange(index =>
                {
                    if (index == path.Length - 1)
                    {
                        float pathLength = 0f;
                        for (int i = path.Length - 1; i > path.Length - 2; i--)
                        {
                            pathLength += Vector3.Distance(path[i], path[i - 1]);
                        }

                        float pathDuration = pathLength / GameManager.Instance.GameConfigManager.vehicleConfig.moveSpeed;
                        transform.DOScale(1.2f, pathDuration).SetEase(Ease.OutBack);
                    }
                })
                .SetSpeedBased(true)
                .OnComplete(() =>
                {
                    wasParked = true;
                    smokeTrail.SetActive(false);
                    isMoving = false;


                    vehicleMeshRenderers[1].gameObject.SetActive(false);
                    MonoBehaviourEventExtensions.DispatchEvent(this, EventID.CarParked);
                });
        }
        else
        {
            transform.GetChild(0).DOMove(blockedPosition.Value, 50f)
                .SetEase(Ease.Linear)
                .SetSpeedBased(true)
                .OnComplete(() =>
                {
                    AudioServer.I.Shot(eAudio.SFX_Vehicle_collision.ToString());

                    blockedByVehicle = GetBlockedByVehicle();
                    if (blockedByVehicle != null && !blockedByVehicle.isMoving)
                    {
                        blockedByVehicle.GetHitDirectionFrom(this);
                    }

                    VibrationManager.Instance.Vibrate(HapticType.Large, 250);
                    transform.GetChild(0).DOMove(originalPosition, 0.25f)
                        .SetEase(Ease.Linear)
                        .OnComplete(() =>
                        {
                            isMoving = false;
                            smokeTrail.SetActive(false);
                        });
                });

        }
    }

    public Transform GetEmptySeat()
    {
        Transform seat = seats[currentIndexSeat];
        currentIndexSeat++;
        return seat;
    }

    public void HandleFliedToParkingSlot()
    {
        wasParked = true;
        isMoving = false;

        vehicleMeshRenderers[1].gameObject.SetActive(false);
        MonoBehaviourEventExtensions.DispatchEvent(this, EventID.CarParked);
    }

    private void MoveVehicleToExitPoint()
    {
        AudioServer.I.Shot(eAudio.SFX_Vehicle_End.ToString());
        GameManager.Instance.ParkingSlotsManager.CheckAvailableSlot();
        Vector3[] path = new Vector3[3]
        {
            transform.position,
            targetParkingSlot.VehicleEntryPoint.position,
            targetParkingSlot.VehicleEntryPoint.position - Vector3.right * 2f,
        };
        arrowMark.SetActive(false);
        seatHolder.SetActive(false);
        vehicleMeshRenderers[1].gameObject.SetActive(true);
        transform.DOScale(1f, 0.75f).SetEase(Ease.Linear);
        transform.DOPath(path, GameManager.Instance.GameConfigManager.vehicleConfig.unparkSpeed, PathType.CatmullRom)
            .SetLookAt(0.01f, Vector3.back)
            .SetEase(GameManager.Instance.GameConfigManager.vehicleConfig.unparkCurve)
            .SetSpeedBased()
            .OnComplete(() =>
            {
                smokeTrail.SetActive(true);
                transform.DOMove(
                        GameManager.Instance.ParkingSlotsManager.ExitPoint.position,
                        GameManager.Instance.GameConfigManager.vehicleConfig.moveSpeed)
                    .SetEase(GameManager.Instance.GameConfigManager.vehicleConfig.moveCurve)
                    .SetSpeedBased(true)
                    .OnComplete(() =>
                    {
                        GameManager.Instance.LevelManager.ReleaseVehicle(this);
                    });
            });
    }

    private List<Vector2Int> GetRelativeFootprint()
    {
        return gridManager.CalculateGridFootprint(Width, Length, GetDirection());
    }

    private void RevealMysteryVehicle(Vehicle blockedByVehicle)
    {
        if (this.blockedByVehicle != blockedByVehicle)
        {
            return;
        }
        Blink(1.0f, () =>
        {
            RevealMysteryVehicle();
        });
    }

    private void RevealMysteryVehicle()
    {
        questionMark.SetActive(false);
        arrowMark.SetActive(true);
        vehicleType = EVehicleType.NormalVehicle;
        foreach (var meshRenderer in vehicleMeshRenderers)
        {
            SetMat(meshRenderer);
        }
    }

    public void SetMysteryState(bool isMystery)
    {
        if (isMystery)
        {
            vehicleType = EVehicleType.MysteryVehicle;
            questionMark.SetActive(true);
            arrowMark.SetActive(false);
        }
        else
        {
            vehicleType = EVehicleType.NormalVehicle;
            questionMark.SetActive(false);
            arrowMark.SetActive(true);
        }
    }

    private void UpdateGridOccupation()
    {
        // List<Vector2Int> footprint = GetCurrentFootprint();
        // gridManager.UpdateObjectOccupation(vehicleId, gameObject, footprint);
    }


    public List<Vector2Int> GetCurrentFootprint()
    {
        Vector2Int currentGridPos = gridManager.WorldToGrid(transform.position);
        List<Vector2Int> relativeFootprint = GetRelativeFootprint();
        return gridManager.GetAbsoluteFootprint(currentGridPos, relativeFootprint);
    }


    public void FillVehicle(HumanoidBlock humanoidBlock)
    {
        if (!HasEmptySeat)
        {
            return;
        }

        sittingHumanoids.Add(humanoidBlock);
    }

    Tween bounceTween;

    private void BounceFillComplete()
    {
        if (bounceTween != null)
        {
            bounceTween.Kill();
        }

        transform.localScale = Vector3.one * 1.2f;
        bounceTween = transform.DOScale(1.3f, 0.1f).SetEase(Ease.OutBack).OnComplete(() =>
        {
            bounceTween = transform.DOScale(1.2f, 0.1f).SetEase(Ease.OutBack);
        });
    }

    public void OnFillComplete()
    {
        BounceFillComplete();
        if (HasEmptySeat)
        {
            return;
        }

        if (sittingHumanoids.Any(humanoid => !humanoid.isSitting))
        {
            return;
        }

        if (vehicleType == EVehicleType.FireTruck)
        {
            this.DispatchEvent(EventID.OnFireRescueEventEnd);
        }
        VibrationManager.Instance.Vibrate(HapticType.Large, 250);
        AudioServer.I.Shot(eAudio.SFX_Vehicle_Full.ToString());
        targetParkingSlot.UnparkVehicle();
        MoveVehicleToExitPoint();
    }

    void IPoolable.OnReuse()
    {
        wasParked = false;
        isMoving = false;
    }

    void IPoolable.OnRelease()
    {
        DOTween.Kill(transform);
        for (int i = 0; i < sittingHumanoids.Count; i++)
        {
            sittingHumanoids[i].gameObject.Release();
        }

        sittingHumanoids.Clear();
        targetParkingSlot = null;

        // Dispatch event for garage vehicles
    }

    public void SetMaterial(Material material)
    {
        foreach (var meshRenderer in vehicleMeshRenderers)
        {
            meshRenderer.material = material;
        }
    }

    public bool IsBlocked()
    {
        return GetBlockedPosition() != null;
    }

    public bool CanSwap()
    {
        return !wasParked && !isMoving && vehicleType == EVehicleType.NormalVehicle;
    }

    public bool CanSwap(Vehicle other = null)
    {
        bool canSwap = !wasParked && !isMoving && vehicleType == EVehicleType.NormalVehicle;
        if (other != null)
        {
            return canSwap && other != this && seats.Length == other.seats.Length;
        }
        return canSwap;
    }

    public void Swap(Vehicle other)
    {
        if (!CanSwap(other))
        {
            return;
        }

        Vector3 tempPosition = transform.position;
        Quaternion tempRotation = transform.rotation;
        transform.position = other.transform.position;
        transform.rotation = other.transform.rotation;
        other.transform.position = tempPosition;
        other.transform.rotation = tempRotation;
    }

    public void Animate()
    {
        DoAnimationVehicle(gameObject);
    }

    public Vector3? GetBlockedPosition()
    {
        Vehicle blockedVehicle;
        return GetBlockedPosition(out blockedVehicle);
    }

    public Vehicle GetBlockedByVehicle()
    {
        Vehicle blockedVehicle;
        GetBlockedPosition(out blockedVehicle);
        return blockedVehicle;
    }

    public Vehicle[] GetBlockedVehicles()
    {
        List<Vehicle> vehicles = new List<Vehicle>();
        Vector3 forwardDirection = transform.forward;

        Vector3 boxSize = boxCollider.size;
        float vehicleWidth = boxSize.x;
        float vehicleLength = boxSize.z;

        Vector3 rightDirection = transform.right;
        Vector3 leftDirection = -rightDirection;

        Vector3 rightRayStart = transform.position + rightDirection * (vehicleWidth * 0.4f);
        Vector3 leftRayStart = transform.position + leftDirection * (vehicleWidth * 0.4f);

        float raycastDistance = 50f;

        RaycastHit rightHit, leftHit;
        bool rightHitDetected = Physics.Raycast(rightRayStart, forwardDirection, out rightHit, raycastDistance);
        bool leftHitDetected = Physics.Raycast(leftRayStart, forwardDirection, out leftHit, raycastDistance);

        blockedByVehicle = null;
        float closestDistance = float.MaxValue;
        Vector3 closestHitPoint = Vector3.zero;
        Vector3 raycastStartPoint = Vector3.zero;

        if (rightHitDetected)
        {
            Vehicle hitVehicle = rightHit.collider.GetComponent<Vehicle>();
            if (!hitVehicle.WasParked && hitVehicle != null && hitVehicle != this)
            {
                blockedByVehicle = hitVehicle;
                closestDistance = rightHit.distance;
                closestHitPoint = rightHit.point;
                raycastStartPoint = rightRayStart;
            }
        }

        if (leftHitDetected)
        {
            Vehicle hitVehicle = leftHit.collider.GetComponent<Vehicle>();
            if (!hitVehicle.WasParked && hitVehicle != null && hitVehicle != this)
            {
                if (leftHit.distance < closestDistance)
                {
                    blockedByVehicle = hitVehicle;
                    closestDistance = leftHit.distance;
                    closestHitPoint = leftHit.point;
                    raycastStartPoint = leftRayStart;
                }
            }
        }
        return vehicles.ToArray();
    }

    public Vector3? GetBlockedPosition(out Vehicle blockedByVehicle)
    {
        Vector3 forwardDirection = transform.forward;

        Vector3 boxSize = boxCollider.size;
        float vehicleWidth = boxSize.x;
        float vehicleLength = boxSize.z;

        Vector3 rightDirection = transform.right;
        Vector3 leftDirection = -rightDirection;

        Vector3 rightRayStart = transform.position + rightDirection * (vehicleWidth * 0.4f);
        Vector3 leftRayStart = transform.position + leftDirection * (vehicleWidth * 0.4f);

        float raycastDistance = 50f;

        RaycastHit rightHit, leftHit;
        bool rightHitDetected = Physics.Raycast(rightRayStart, forwardDirection, out rightHit, raycastDistance);
        bool leftHitDetected = Physics.Raycast(leftRayStart, forwardDirection, out leftHit, raycastDistance);

        blockedByVehicle = null;
        float closestDistance = float.MaxValue;
        Vector3 closestHitPoint = Vector3.zero;
        Vector3 raycastStartPoint = Vector3.zero;

        if (rightHitDetected)
        {
            Vehicle hitVehicle = rightHit.collider.GetComponent<Vehicle>();
            if (!hitVehicle.WasParked && hitVehicle != null && hitVehicle != this)
            {
                blockedByVehicle = hitVehicle;
                closestDistance = rightHit.distance;
                closestHitPoint = rightHit.point;
                raycastStartPoint = rightRayStart;
            }
        }

        if (leftHitDetected)
        {
            Vehicle hitVehicle = leftHit.collider.GetComponent<Vehicle>();
            if (!hitVehicle.WasParked && hitVehicle != null && hitVehicle != this)
            {
                if (leftHit.distance < closestDistance)
                {
                    blockedByVehicle = hitVehicle;
                    closestDistance = leftHit.distance;
                    closestHitPoint = leftHit.point;
                    raycastStartPoint = leftRayStart;
                }
            }
        }

        if (blockedByVehicle == null)
        {
            return null;
        }

        Vector3 centerToCorner = raycastStartPoint - transform.position;

        float cornerOffsetInForward = Vector3.Dot(centerToCorner, forwardDirection);

        float centerDistanceToObstacle = closestDistance - cornerOffsetInForward;
        float selfHalfLength = vehicleLength * 0.5f;
        float safetyMargin = 0.05f;

        Vector3 intersectionPoint = transform.position + forwardDirection * centerDistanceToObstacle;
        Vector3 blockedPosition = intersectionPoint - forwardDirection * (selfHalfLength + safetyMargin);

        return blockedPosition;
    }

    public void GetHitDirectionFrom(Vehicle attackerVehicle)
    {
        if (isMoving || anim.enabled) return;

        anim.enabled = true;
        Direction direction;
        Vector3 impactDirection = transform.position - attackerVehicle.transform.position;
        impactDirection.Normalize();

        float forwardDot = Vector3.Dot(impactDirection, transform.forward);
        float rightDot = Vector3.Dot(impactDirection, transform.right);

        if (Mathf.Abs(forwardDot) - Mathf.Abs(rightDot) >= 0.3f)
        {
            if (forwardDot > 0)
            {
                direction = Direction.Down;
            }
            else
            {
                direction = rightDot > 0 ? Direction.Left : Direction.Right;
            }

        }
        else
        {
            direction = rightDot > 0 ? Direction.Left : Direction.Right;
        }
        anim.SetTrigger("Impact");
        anim.SetInteger("ImpactID", (int)direction);
    }

    public void SetTargetParkingSlot(ParkingSlot parkingSlot)
    {
        targetParkingSlot = parkingSlot;
    }

    public void Blink(float duration = 1f, Action onComplete = null)
    {
        StartCoroutine(BlinkMaterials(duration, onComplete));
    }

    private IEnumerator BlinkMaterials(float duration, Action onComplete = null)
    {
        var startTime = Time.time;
        var originalMaterial = vehicleMeshRenderers[0].material;
        SetMaterial(GameManager.I.ColorManager.gradientMaterial);
        while (Time.time - startTime < duration)
        {
            var blinkSpeed = GameManager.I.ColorManager.blinkSpeed;
            for (int i = 0; i < vehicleMeshRenderers.Length; i++)
            {
                var material = vehicleMeshRenderers[i].material;
                // var tex = material.GetTextureOffset("_AlbedoMap");
                // material.SetTextureOffset("_AlbedoMap", new Vector2(Time.time * blinkSpeed, tex.y));
                material.SetFloat("Vector1_3E512A80", Time.time * blinkSpeed);
                material.SetFloat("Vector1_8D0C51A0", Time.time * blinkSpeed);
            }
            yield return new WaitForEndOfFrame();
        }
        SetMaterial(originalMaterial);
        onComplete?.Invoke();
    }

    private void DoAnimationVehicle(GameObject vehicle, Action onComplete = null)
    {
        vehicle.transform.localScale = Vector3.zero;
        vehicle.transform.DOScale(Vector3.one, 0.2f).SetEase(Ease.OutBack);
        vehicle.transform.DOMoveY(vehicle.transform.position.y + 1, 0.2f).SetEase(Ease.OutBack).OnComplete(() =>
        {
            vehicle.transform.DOMoveY(vehicle.transform.position.y - 1, 0.2f).SetEase(Ease.OutBack).OnComplete(() =>
            {
                onComplete?.Invoke();
            });
        });
    }
}
