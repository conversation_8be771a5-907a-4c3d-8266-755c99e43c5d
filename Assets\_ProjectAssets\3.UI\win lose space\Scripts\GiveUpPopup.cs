using DSDK.Data;
using DSDK.UISystem;
using UnityEngine;

public class GiveUpPopup : UIPanel
{
    [SerializeField] private BaseBtn closeBtn;
    [SerializeField] private BaseBtn retryBtn;
    [SerializeField] private BaseBtn payBtn;

    void Start()
    {
        closeBtn.OnClick(OnCloseBtnClick);
        retryBtn.OnClick(OnCloseBtnClick);
        payBtn.OnClick(OnPayBtnClick);
    }
    public override void Show()
    {
        base.Show();
        payBtn.CanInvokeAction = GameManager.Instance.GetCurrentCoin() >= GameManager.Instance.GameConfigManager.RevivePrice;
    }

    private void OnCloseBtnClick()
    {
        GameManager.Instance.BackToHomeScreen();
        Hide();
    }

    private void OnPayBtnClick()
    {
        if (GameManager.Instance.ReviveByCoin())
        {
            Hide();
        }
    }
}
