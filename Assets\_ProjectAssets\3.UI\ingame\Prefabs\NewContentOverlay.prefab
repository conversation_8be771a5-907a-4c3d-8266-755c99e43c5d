%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &17780031044829881
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6704290268035798936}
  - component: {fileID: 2373783510354612523}
  - component: {fileID: 1313387971502722990}
  m_Layer: 5
  m_Name: Context
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &6704290268035798936
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 17780031044829881}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8899529320363031654}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0}
  m_AnchorMax: {x: 0.5, y: 0}
  m_AnchoredPosition: {x: 0, y: 417}
  m_SizeDelta: {x: 694, y: 53}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &2373783510354612523
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 17780031044829881}
  m_CullTransparentMesh: 1
--- !u!114 &1313387971502722990
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 17780031044829881}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Play the next level to test it out
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: a5115ad839bae5e4bbc1fe9a87f76006, type: 2}
  m_sharedMaterial: {fileID: 4135029120913929601, guid: a5115ad839bae5e4bbc1fe9a87f76006, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 45
  m_fontSizeBase: 45
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!1 &4216369050639117465
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4578106851047637551}
  - component: {fileID: 9130209979925942705}
  - component: {fileID: 2908093666280639382}
  m_Layer: 5
  m_Name: NewContentDescription
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &4578106851047637551
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4216369050639117465}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8899529320363031654}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: 0, y: 566.64233}
  m_SizeDelta: {x: 0, y: 230.7154}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &9130209979925942705
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4216369050639117465}
  m_CullTransparentMesh: 1
--- !u!114 &2908093666280639382
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4216369050639117465}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: "Fire truck has priority. \LMove it to the queue within 30 seconds after
    it appears."
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: a5115ad839bae5e4bbc1fe9a87f76006, type: 2}
  m_sharedMaterial: {fileID: 4135029120913929601, guid: a5115ad839bae5e4bbc1fe9a87f76006, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 45
  m_fontSizeBase: 45
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!1 &4315937408602121060
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7433708820768314161}
  - component: {fileID: 7507503730746596156}
  - component: {fileID: 8368815973179578535}
  m_Layer: 5
  m_Name: NewContentName
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7433708820768314161
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4315937408602121060}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8899529320363031654}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 1}
  m_AnchorMax: {x: 0.5, y: 1}
  m_AnchoredPosition: {x: 0, y: -475.5}
  m_SizeDelta: {x: 1080, y: 106}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &7507503730746596156
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4315937408602121060}
  m_CullTransparentMesh: 1
--- !u!114 &8368815973179578535
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4315937408602121060}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: The <style=green>Mystery Block</style>  is now available!
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: a5115ad839bae5e4bbc1fe9a87f76006, type: 2}
  m_sharedMaterial: {fileID: 4135029120913929601, guid: a5115ad839bae5e4bbc1fe9a87f76006, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 45
  m_fontSizeBase: 45
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!1 &4487413843033355365
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2055278724016833055}
  - component: {fileID: 4522227532820707610}
  - component: {fileID: 395124758068418709}
  m_Layer: 5
  m_Name: ContinueText
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2055278724016833055
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4487413843033355365}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8899529320363031654}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0}
  m_AnchorMax: {x: 0.5, y: 0}
  m_AnchoredPosition: {x: 0, y: 301.5}
  m_SizeDelta: {x: 558, y: 71}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &4522227532820707610
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4487413843033355365}
  m_CullTransparentMesh: 1
--- !u!114 &395124758068418709
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4487413843033355365}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: TAP TO CONTINUE
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: a5115ad839bae5e4bbc1fe9a87f76006, type: 2}
  m_sharedMaterial: {fileID: 4135029120913929601, guid: a5115ad839bae5e4bbc1fe9a87f76006, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 60
  m_fontSizeBase: 60
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!1 &5544306397384828450
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8481114831734208372}
  - component: {fileID: 7659818847734428341}
  - component: {fileID: 8472937367237384629}
  m_Layer: 5
  m_Name: Title
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &8481114831734208372
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5544306397384828450}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8899529320363031654}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 1}
  m_AnchorMax: {x: 0.5, y: 1}
  m_AnchoredPosition: {x: 0, y: -274.5}
  m_SizeDelta: {x: 727, y: 163}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &7659818847734428341
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5544306397384828450}
  m_CullTransparentMesh: 1
--- !u!114 &8472937367237384629
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5544306397384828450}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 26b38993deefb31408f68f58e01900bd, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1 &6231606923551383386
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3784132573076768420}
  - component: {fileID: 1655051664013508422}
  - component: {fileID: 3934893923286816652}
  m_Layer: 5
  m_Name: NewContentImage
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3784132573076768420
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6231606923551383386}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8899529320363031654}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 78}
  m_SizeDelta: {x: 806, y: 468}
  m_Pivot: {x: 0.5, y: 0.475}
--- !u!222 &1655051664013508422
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6231606923551383386}
  m_CullTransparentMesh: 1
--- !u!114 &3934893923286816652
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6231606923551383386}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 72b41f40d822936419d7ad8000783a4a, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1 &6908196188770171433
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8899529320363031654}
  - component: {fileID: 8714488661833806580}
  - component: {fileID: 9129546485017358945}
  - component: {fileID: 5085123009339974234}
  - component: {fileID: 1141818394707483929}
  - component: {fileID: 136164414316940471}
  - component: {fileID: 4552492710409049893}
  m_Layer: 5
  m_Name: NewContentOverlay
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &8899529320363031654
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6908196188770171433}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8481114831734208372}
  - {fileID: 7433708820768314161}
  - {fileID: 5437770558069150746}
  - {fileID: 3784132573076768420}
  - {fileID: 4578106851047637551}
  - {fileID: 6704290268035798936}
  - {fileID: 2055278724016833055}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &8714488661833806580
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6908196188770171433}
  m_CullTransparentMesh: 1
--- !u!114 &9129546485017358945
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6908196188770171433}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.023529412, g: 0.07450981, b: 0.1882353, a: 0.99607843}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!114 &5085123009339974234
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6908196188770171433}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 75f1b53e438128d48ad0f1eb1d63298d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ID: NewContentOverlay
  PanelType: 2
  newContentName: {fileID: 8368815973179578535}
  newContentDescription: {fileID: 2908093666280639382}
  contnueText: {fileID: 4487413843033355365}
  newContentImage: {fileID: 3934893923286816652}
  continueButton: {fileID: 1141818394707483929}
--- !u!114 &1141818394707483929
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6908196188770171433}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 0
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 9129546485017358945}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &136164414316940471
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6908196188770171433}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 32fb48558058f5b47838af8307f70f5f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fadeInSettings:
    isFadeIn: 1
    canvasGroup: {fileID: 4552492710409049893}
    fadeInDuration: 0.1
    easeFadeIn: 1
  scaleSettings:
    isSetScale: 0
    startScaleValue: {x: 0, y: 0, z: 0}
    scaleDuration: 0.1
    easeScale: 27
  positionSettings:
    isSetPos: 0
    startPos: {fileID: 0}
    moveDuration: 0.5
    easeMove: 27
  animationSettings:
    setUpdate: 0
    delay: 0
  idleScaleSettings:
    isIdleScale: 0
    idleScaleDuration: 1
    idleEase: 1
--- !u!225 &4552492710409049893
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6908196188770171433}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!1 &8264955129031845693
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5437770558069150746}
  - component: {fileID: 8769835247541183285}
  - component: {fileID: 1248460225254184705}
  m_Layer: 5
  m_Name: UIParticle_1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &5437770558069150746
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8264955129031845693}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6708087795585040387}
  m_Father: {fileID: 8899529320363031654}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 75}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &8769835247541183285
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8264955129031845693}
  m_CullTransparentMesh: 1
--- !u!114 &1248460225254184705
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8264955129031845693}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 126a48decbe434acb9f51716d92c3db0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_IsTrail: 0
  m_IgnoreCanvasScaler: 0
  m_AbsoluteMode: 0
  m_Scale3D: {x: 400, y: 400, z: 400}
  m_AnimatableProperties: []
  m_Particles:
  - {fileID: 6901626364764544639}
  m_MeshSharing: 0
  m_GroupId: 0
  m_GroupMaxId: 0
  m_PositionMode: 0
  m_AutoScaling: 0
  m_AutoScalingMode: 2
  m_UseCustomView: 0
  m_CustomViewSize: 10
  m_TimeScaleMultiplier: 1
--- !u!1001 &6703509609161571277
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 5437770558069150746}
    m_Modifications:
    - target: {fileID: 1831709892743530, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: m_Name
      value: ItemSparkleSoftBlue
      objectReference: {fileID: 0}
    - target: {fileID: 4587395169450958, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4587395169450958, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4587395169450958, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4587395169450958, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 4587395169450958, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 4587395169450958, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4587395169450958, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4587395169450958, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: 4587395169450958, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4587395169450958, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: ColorModule.enabled
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: RotationModule.enabled
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: InitialModule.rotation3D
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: EmissionModule.m_BurstCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: RotationModule.curve.scalar
      value: 1.3089969
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: RotationModule.x.minMaxState
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: RotationModule.y.minMaxState
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: RotationModule.curve.minMaxState
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: EmissionModule.m_Bursts.Array.size
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: EmissionModule.rateOverTime.scalar
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: InitialModule.startLifetime.scalar
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: InitialModule.startColor.maxColor.a
      value: 0.49803922
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: InitialModule.startColor.maxColor.b
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: InitialModule.startColor.maxColor.g
      value: 0.9176218
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: InitialModule.startColor.maxColor.r
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: ColorModule.gradient.maxGradient.atime0
      value: 386
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: ColorModule.gradient.maxGradient.atime1
      value: 30647
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: ColorModule.gradient.maxGradient.atime2
      value: 65535
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: ColorModule.gradient.maxGradient.atime3
      value: 33924
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: ColorModule.gradient.maxGradient.key0.a
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: ColorModule.gradient.maxGradient.key1.a
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: ColorModule.gradient.maxGradient.key2.a
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: ColorModule.gradient.maxGradient.key3.a
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: ColorModule.gradient.maxGradient.m_ColorSpace
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: ColorModule.gradient.maxGradient.m_NumAlphaKeys
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: EmissionModule.m_Bursts.Array.data[0].cycleCount
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: EmissionModule.m_Bursts.Array.data[0].probability
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: EmissionModule.m_Bursts.Array.data[0].repeatInterval
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[0].value
      value: 0.51571655
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: EmissionModule.m_Bursts.Array.data[0].countCurve.scalar
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[0].inSlope
      value: 1.0755264
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[1].inSlope
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[0].inWeight
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[0].outSlope
      value: 1.0755264
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[1].inWeight
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[1].outSlope
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[0].outWeight
      value: 0.122807
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[1].outWeight
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: EmissionModule.m_Bursts.Array.data[0].countCurve.minScalar
      value: 30
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[0].tangentMode
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[1].tangentMode
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: EmissionModule.m_Bursts.Array.data[0].countCurve.maxCurve.m_PreInfinity
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: EmissionModule.m_Bursts.Array.data[0].countCurve.minCurve.m_PreInfinity
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: EmissionModule.m_Bursts.Array.data[0].countCurve.maxCurve.m_PostInfinity
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: EmissionModule.m_Bursts.Array.data[0].countCurve.minCurve.m_PostInfinity
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: EmissionModule.m_Bursts.Array.data[0].countCurve.maxCurve.m_RotationOrder
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: EmissionModule.m_Bursts.Array.data[0].countCurve.minCurve.m_RotationOrder
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: EmissionModule.m_Bursts.Array.data[0].countCurve.maxCurve.m_Curve.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: EmissionModule.m_Bursts.Array.data[0].countCurve.minCurve.m_Curve.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: EmissionModule.m_Bursts.Array.data[0].countCurve.maxCurve.m_Curve.Array.data[1].time
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: EmissionModule.m_Bursts.Array.data[0].countCurve.minCurve.m_Curve.Array.data[1].time
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: EmissionModule.m_Bursts.Array.data[0].countCurve.maxCurve.m_Curve.Array.data[0].value
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: EmissionModule.m_Bursts.Array.data[0].countCurve.maxCurve.m_Curve.Array.data[1].value
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: EmissionModule.m_Bursts.Array.data[0].countCurve.minCurve.m_Curve.Array.data[0].value
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: EmissionModule.m_Bursts.Array.data[0].countCurve.minCurve.m_Curve.Array.data[1].value
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 199593643502874416, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
--- !u!4 &6708087795585040387 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4587395169450958, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
  m_PrefabInstance: {fileID: 6703509609161571277}
  m_PrefabAsset: {fileID: 0}
--- !u!198 &6901626364764544639 stripped
ParticleSystem:
  m_CorrespondingSourceObject: {fileID: 198201152053765554, guid: 66f193da1de9a3d4cada4e2c914e8d3d, type: 3}
  m_PrefabInstance: {fileID: 6703509609161571277}
  m_PrefabAsset: {fileID: 0}
