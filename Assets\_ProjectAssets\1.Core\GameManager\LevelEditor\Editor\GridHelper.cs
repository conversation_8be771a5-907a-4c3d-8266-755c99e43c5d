using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace DSDK.LevelEditor
{
    public static class GridHelper
    {
        public static void DrawCell(Rect rect, Color color)
        {
            Color originalColor = GUI.color;
            GUI.color = color;
            GUI.DrawTexture(rect, Texture2D.whiteTexture);
            GUI.color = originalColor;
        }

        public static void DrawDashedRect(Rect rect)
        {
            float dashLength = 3f;

            for (float x = rect.x; x < rect.x + rect.width; x += dashLength * 2)
            {
                float width = Mathf.Min(dashLength, rect.x + rect.width - x);
                GUI.DrawTexture(new Rect(x, rect.y, width, 1), EditorGUIUtility.whiteTexture);
            }

            for (float x = rect.x; x < rect.x + rect.width; x += dashLength * 2)
            {
                float width = Mathf.Min(dashLength, rect.x + rect.width - x);
                GUI.DrawTexture(new Rect(x, rect.y + rect.height - 1, width, 1), EditorGUIUtility.whiteTexture);
            }

            for (float y = rect.y; y < rect.y + rect.height; y += dashLength * 2)
            {
                float height = Mathf.Min(dashLength, rect.y + rect.height - y);
                GUI.DrawTexture(new Rect(rect.x, y, 1, height), EditorGUIUtility.whiteTexture);
            }

            for (float y = rect.y; y < rect.y + rect.height; y += dashLength * 2)
            {
                float height = Mathf.Min(dashLength, rect.y + rect.height - y);
                GUI.DrawTexture(new Rect(rect.x + rect.width - 1, y, 1, height), EditorGUIUtility.whiteTexture);
            }
        }

        public static void DrawUILine(Color color, int thickness = 2, int padding = 10)
        {
            Rect r = EditorGUILayout.GetControlRect(GUILayout.Height(padding + thickness));
            r.height = thickness;
            r.y += padding / 2;
            r.x -= 2;
            r.width += 6;
            EditorGUI.DrawRect(r, color);
        }
    }
}