using DSDK.Audio;
using DSDK.Data;
using DSDK.UISystem;
using UnityEngine;

public class HomeScreenManager : MonoBehaviour
{
    public enum OfferType
    {
        None,
        FirstTimeShowInterstitial,
        FirstTimeBackHome
    }

#pragma warning disable UDR0001 // Domain Reload Analyzer
    public static bool IsShowOffer = false;
    public static OfferType ShowOfferType = OfferType.None;
#pragma warning restore UDR0001 // Domain Reload Analyzer


    void Start()
    {
        UISystem.Instance.ShowPanel<HomeMenu>();
        // AudioServer.Instance.PlayMusic(eAudio.SFX_BGM.ToString());
        CheckShowOffer();
    }

    void CheckShowOffer()
    {
        if (!IsShowOffer)
        {
            return;
        }
        IsShowOffer = false;

        if (!GameData.I.IsAdsRemovePurchased || ShowOfferType == OfferType.FirstTimeShowInterstitial)
        {
            UISystem.Instance.ShowPanel<NoAdsPopup>(ShowOfferCallback);
            return;
        }

        if (GameData.I.TotalPurchased < 15)
        {
            UISystem.Instance.ShowPanel<OfferSmallPackPopup>(ShowOfferCallback);
            return;
        }

        if (GameData.I.TotalPurchased < 35)
        {
            UISystem.Instance.ShowPanel<OfferMediumPopup>(ShowOfferCallback);
            return;
        }

        UISystem.Instance.ShowPanel<OfferLargePackPopup>(ShowOfferCallback);
        return;
    }

    void ShowOfferCallback(UIPanel panel)
    {
        if (ShowOfferType == OfferType.FirstTimeShowInterstitial)
        {
            GameData.I.IsFirstTimeShowInterstitial = false;
        }

        if (ShowOfferType == OfferType.FirstTimeBackHome)
        {
            GameData.I.IsFirstTimeBackHome = false;
        }

        ShowOfferType = OfferType.None;
    }
}
