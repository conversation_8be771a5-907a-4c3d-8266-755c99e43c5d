using UnityEngine;
using UnityEditor;
using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Reflection;
using DSDK.Logger;
using DSDK.Data;

namespace DSDK.Data.Editor
{
    [InitializeOnLoad]
    public class DataMonitorWindow : EditorWindow
    {
        private Vector2 _scrollPosition;
        private DataMonitor _dataSetting;
        private SerializedObject _serializedObject;
        private UnityEditor.Editor _editor;
        private GameObject _prefab;
        private List<System.Type> _dataTypes = new List<System.Type>();
        private bool _stylesInitialized;

        // UI Styles
        private GUIStyle _headerStyle;
        private GUIStyle _sectionHeaderStyle;
        private GUIStyle _buttonStyle;
        private GUIStyle _boxStyle;
        private GUIStyle _titleStyle;
        private Color _headerColor = new Color(0.2f, 0.4f, 0.7f);
        private Color _buttonColor = new Color(0.3f, 0.5f, 0.8f);
        private Texture2D _logoTexture;

        private const string PREFAB_PATH = "DataMonitor";
        private const string SCRIPT_TEMPLATE = @"using DSDK.Data;

namespace {0}
{{
    [System.Serializable]
    public partial class {1} : Data<{1}>
    {{
        // TODO: Add your data fields here
    }}
}}";

        private const string SCRIPT_LOGIC_TEMPLATE = @"using DSDK.Data;

namespace {0}
{{
    public partial class {1}
    {{
        // TODO: Add your logic here
    }}
}}";

        [MenuItem("Dmobin/Data Monitor")]
        public static void ShowWindow()
        {
            var window = GetWindow<DataMonitorWindow>("Data Monitor");
            window.Show();
        }

        private void OnEnable()
        {
            titleContent = new GUIContent("Data Monitor");
            _stylesInitialized = false;

            EditorApplication.playModeStateChanged += OnPlayModeStateChanged;
            AssemblyReloadEvents.afterAssemblyReload += OnAfterAssemblyReload;

            FindDataSetting();
        }

        private void OnDisable()
        {
            AssemblyReloadEvents.afterAssemblyReload -= OnAfterAssemblyReload;
            EditorApplication.playModeStateChanged -= OnPlayModeStateChanged;
        }

        private void OnPlayModeStateChanged(PlayModeStateChange state)
        {
            FindDataSetting();
            Repaint();
        }

        private void OnAfterAssemblyReload()
        {
            FindDataSetting();
            Repaint();
            // CheckForNewDataScripts();
        }

        private void InitializeStyles()
        {
            _headerStyle = new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 14,
                alignment = TextAnchor.MiddleLeft,
                margin = new RectOffset(5, 5, 5, 5)
            };

            _sectionHeaderStyle = new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 12,
                alignment = TextAnchor.MiddleLeft,
                margin = new RectOffset(5, 5, 8, 5)
            };

            _buttonStyle = new GUIStyle(GUI.skin.button)
            {
                fontSize = 12,
                fontStyle = FontStyle.Bold,
                alignment = TextAnchor.MiddleCenter,
                margin = new RectOffset(5, 5, 3, 3),
                padding = new RectOffset(8, 8, 4, 4)
            };

            _boxStyle = new GUIStyle(EditorStyles.helpBox)
            {
                margin = new RectOffset(5, 5, 5, 5),
                padding = new RectOffset(10, 10, 10, 10)
            };

            _titleStyle = new GUIStyle(EditorStyles.largeLabel)
            {
                fontSize = 16,
                fontStyle = FontStyle.Bold,
                alignment = TextAnchor.MiddleCenter,
                margin = new RectOffset(0, 0, 10, 15)
            };

            _stylesInitialized = true;
        }

        private void OnGUI()
        {
            // Đảm bảo styles đã được khởi tạo
            if (!_stylesInitialized)
            {
                if (EditorStyles.boldLabel != null)
                {
                    InitializeStyles();
                }
                else
                {
                    EditorGUILayout.LabelField("Đang khởi tạo...");
                    return;
                }
            }

            // Draw Banner with Logo
            DrawBanner();

            // Main content
            using (new EditorGUILayout.VerticalScope())
            {
                DrawHeader();

                EditorGUILayout.Space(10);

                if (_dataSetting == null || _serializedObject == null)
                {
                    DrawNoMonitorState();
                    return;
                }

                EditorGUILayout.Space(10);
                using (new EditorGUILayout.VerticalScope(_boxStyle))
                {
                    EditorGUILayout.LabelField("Cấu hình Data Monitor", _sectionHeaderStyle);
                    EditorGUILayout.Space(5);

                    using (var scrollView = new EditorGUILayout.ScrollViewScope(_scrollPosition))
                    {
                        _scrollPosition = scrollView.scrollPosition;
                        DrawDataSettingInfo();

                        if (Application.isPlaying)
                        {
                            EditorGUILayout.Space(10);
                            DrawDataStats();
                        }
                    }
                }

                EditorGUILayout.Space(10);
                DrawFooter();
            }

            // Auto repaint để cập nhật realtime
            if (Application.isPlaying)
            {
                Repaint();
            }
        }

        private void DrawBanner()
        {
            Rect bannerRect = EditorGUILayout.GetControlRect(false, 40);
            EditorGUI.DrawRect(bannerRect, _headerColor);

            GUI.color = Color.white;
            EditorGUI.LabelField(bannerRect, "DATA MONITOR", _titleStyle);
            GUI.color = Color.white;

            EditorGUILayout.Space(5);
        }

        private void DrawNoMonitorState()
        {
            using (new EditorGUILayout.VerticalScope(_boxStyle))
            {
                EditorGUILayout.HelpBox($"Không tìm thấy DataMonitor Prefab tại {PREFAB_PATH}", MessageType.Warning);

                EditorGUILayout.Space(10);

                using (new EditorGUILayout.HorizontalScope())
                {
                    GUILayout.FlexibleSpace();
                    if (GUILayout.Button("Mở thư mục Resources", _buttonStyle, GUILayout.Width(200)))
                    {
                        EditorGUIUtility.PingObject(_dataSetting);
                    }
                    GUILayout.FlexibleSpace();
                }
            }
        }

        private void DrawHeader()
        {
            using (new EditorGUILayout.VerticalScope(EditorStyles.helpBox))
            {
                string playModeInfo = Application.isPlaying
                    ? "Chế độ: Play - Sử dụng Instance trong Scene"
                    : "Chế độ: Edit - Sử dụng Prefab từ Resources";

                EditorGUILayout.LabelField(playModeInfo, EditorStyles.centeredGreyMiniLabel);

                using (new EditorGUILayout.HorizontalScope())
                {
                    GUI.backgroundColor = _buttonColor;

                    if (GUILayout.Button("Thêm Data", _buttonStyle, GUILayout.ExpandWidth(true)))
                    {
                        CreateNewDataScript();
                    }

                    if (GUILayout.Button("Tạo Instance", _buttonStyle, GUILayout.ExpandWidth(true)))
                    {
                        GenerateMonitorInstances();
                    }

                    if (GUILayout.Button("Ping", _buttonStyle, GUILayout.Width(80)))
                    {
                        if (Application.isPlaying)
                            EditorGUIUtility.PingObject(_dataSetting.gameObject);
                        else
                            EditorGUIUtility.PingObject(_prefab);
                    }

                    GUI.backgroundColor = Color.white;
                }
            }
        }

        private void DrawGenerateInstanceButton()
        {
            // Chức năng này đã được chuyển lên phần DrawHeader
        }

        private void GenerateMonitorInstances()
        {
            try
            {
                EditorUtility.DisplayProgressBar("Đang xử lý", "Đang tìm kiếm các DataMonitor.cs file...", 0.1f);

                // Find the DataMonitor.cs file
                string[] guids = AssetDatabase.FindAssets("DataMonitor t:Script");
                string dataMonitorPath = "";

                foreach (string guid in guids)
                {
                    string path = AssetDatabase.GUIDToAssetPath(guid);
                    if (path.EndsWith("DataMonitor.cs"))
                    {
                        dataMonitorPath = path;
                        break;
                    }
                }

                if (string.IsNullOrEmpty(dataMonitorPath))
                {
                    EditorUtility.ClearProgressBar();
                    EditorUtility.DisplayDialog("Lỗi", "Không tìm thấy file DataMonitor.cs", "OK");
                    return;
                }

                EditorUtility.DisplayProgressBar("Đang xử lý", "Đang đọc file DataMonitor.cs...", 0.2f);
                // Read the DataMonitor.cs file
                string scriptContent = System.IO.File.ReadAllText(dataMonitorPath);

                var dataTypes = new List<System.Type>();
                EditorUtility.DisplayProgressBar("Đang xử lý", "Đang tìm kiếm các lớp Data...", 0.3f);
                var assemblies = System.AppDomain.CurrentDomain.GetAssemblies();

                foreach (var assembly in assemblies)
                {
                    try
                    {
                        var types = assembly.GetTypes()
                            .Where(t => t.IsClass && !t.IsAbstract)
                            .Where(t => t.Namespace != null && t.Namespace.StartsWith("DSDK.Data"))
                            .Where(t =>
                            {
                                var baseType = t.BaseType;
                                // Check if type inherits from any Data<> generic class
                                while (baseType != null)
                                {
                                    if (baseType.IsGenericType &&
                                        baseType.GetGenericTypeDefinition().Name.StartsWith("Data`"))
                                    {
                                        return true;
                                    }
                                    baseType = baseType.BaseType;
                                }
                                return false;
                            });

                        dataTypes.AddRange(types);
                    }
                    catch (Exception)
                    {
                        // Skip problematic assemblies
                        continue;
                    }
                }

                // Add any additional types from DataBase if that method exists and finds different types
                try
                {
                    EditorUtility.DisplayProgressBar("Đang xử lý", "Đang lấy dữ liệu từ DataBase...", 0.5f);
                    var dbTypes = DataBase.GetAllDataTypes();
                    if (dbTypes != null)
                    {
                        foreach (var type in dbTypes)
                        {
                            if (!dataTypes.Contains(type))
                            {
                                dataTypes.Add(type);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    // Log lỗi nhưng vẫn tiếp tục thực hiện với các kiểu dữ liệu đã tìm thấy
                    DLogger.LogWarning($"Không thể lấy dữ liệu từ DataBase.GetAllDataTypes(): {ex.Message}");
                }

                // Log the found data types
                DLogger.LogDebug($"Found {dataTypes.Count} data types to add to DataMonitor:");
                foreach (var type in dataTypes)
                {
                    DLogger.LogDebug($"- {type.FullName}");
                }

                if (dataTypes.Count == 0)
                {
                    EditorUtility.ClearProgressBar();
                    EditorUtility.DisplayDialog("Cảnh báo", "Không tìm thấy lớp Data nào. Hãy kiểm tra lại các script của bạn.", "OK");
                    return;
                }

                EditorUtility.DisplayProgressBar("Đang xử lý", "Đang cập nhật file DataMonitor.cs...", 0.7f);

                try
                {
                    // replace #region Monitor Fields
                    var monitorFields = scriptContent.IndexOf("#region Monitor Fields");
                    var endMonitorFields = scriptContent.IndexOf("#endregion", monitorFields);
                    var bodyMonitorFields = scriptContent.Substring(monitorFields, endMonitorFields - monitorFields);
                    var newBodyMonitorFields = "#region Monitor Fields\n\t";
                    foreach (var type in dataTypes)
                    {
                        newBodyMonitorFields += $"        [SerializeField] private {type.Name} _{type.Name.ToLowerInvariant()};\n\t";
                    }
                    scriptContent = scriptContent.Replace(bodyMonitorFields, newBodyMonitorFields);

                    // replace #region Get Instance Default
                    var getInstanceMethod = scriptContent.IndexOf("#region Get Default");
                    var endGetInstanceMethod = scriptContent.IndexOf("#endregion", getInstanceMethod);
                    var methodBody = scriptContent.Substring(getInstanceMethod, endGetInstanceMethod - getInstanceMethod);
                    var newMethodBody = "#region Get Default\n\t\t\t\t";
                    foreach (var type in dataTypes)
                    {
                        newMethodBody += $"if (!FileDataHandler.Instance.IsExist(_{type.Name.ToLowerInvariant()}.Key)) {type.Name}.SetInstance(_{type.Name.ToLowerInvariant()});\n\t\t\t\t";
                    }
                    scriptContent = scriptContent.Replace(methodBody, newMethodBody);

                    // replace #region Get Instance
                    getInstanceMethod = scriptContent.IndexOf("#region Get Instance");
                    endGetInstanceMethod = scriptContent.IndexOf("#endregion", getInstanceMethod);
                    methodBody = scriptContent.Substring(getInstanceMethod, endGetInstanceMethod - getInstanceMethod);
                    newMethodBody = "#region Get Instance\n\t\t\t";
                    foreach (var type in dataTypes)
                    {
                        newMethodBody += $"_{type.Name.ToLowerInvariant()} = {type.Name}.Instance;\n\t\t\t";
                    }
                    scriptContent = scriptContent.Replace(methodBody, newMethodBody);

                    // replace #region Load All Data
                    var loadAllDataMethod = scriptContent.IndexOf("#region Load All Data");
                    var endLoadAllDataMethod = scriptContent.IndexOf("#endregion", loadAllDataMethod);
                    var bodyLoadAllDataMethod = scriptContent.Substring(loadAllDataMethod, endLoadAllDataMethod - loadAllDataMethod);
                    var newBodyLoadAllDataMethod = "#region Load All Data\n\t\t\t";
                    foreach (var type in dataTypes)
                    {
                        newBodyLoadAllDataMethod += $"{type.Name}.Instance.Load();_{type.Name.ToLowerInvariant()} = {type.Name}.Instance;\n\t\t\t";
                    }
                    scriptContent = scriptContent.Replace(bodyLoadAllDataMethod, newBodyLoadAllDataMethod);

                    // replace #region Save All Data
                    var saveAllDataMethod = scriptContent.IndexOf("#region Save All Data");
                    var endSaveAllDataMethod = scriptContent.IndexOf("#endregion", saveAllDataMethod);
                    var bodySaveAllDataMethod = scriptContent.Substring(saveAllDataMethod, endSaveAllDataMethod - saveAllDataMethod);
                    var newBodySaveAllDataMethod = "#region Save All Data\n\t\t\t";
                    foreach (var type in dataTypes)
                    {
                        newBodySaveAllDataMethod += $"_{type.Name.ToLowerInvariant()}.Save();\n\t\t\t";
                    }
                    scriptContent = scriptContent.Replace(bodySaveAllDataMethod, newBodySaveAllDataMethod);

                    EditorUtility.DisplayProgressBar("Đang xử lý", "Đang lưu file...", 0.9f);
                    // Write the updated content back to the file
                    System.IO.File.WriteAllText(dataMonitorPath, scriptContent);
                }
                catch (Exception ex)
                {
                    EditorUtility.ClearProgressBar();
                    DLogger.LogError($"Lỗi khi cập nhật file: {ex.Message}");
                    EditorUtility.DisplayDialog("Lỗi", $"Không thể cập nhật file DataMonitor.cs: {ex.Message}", "OK");
                    return;
                }

                AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);

                EditorUtility.ClearProgressBar();
                EditorUtility.DisplayDialog("Thành công", $"DataMonitor.cs đã được cập nhật với {dataTypes.Count} data types", "OK");
            }
            catch (Exception ex)
            {
                EditorUtility.ClearProgressBar();
                DLogger.LogError($"Error generating instance: {ex.Message}");
                EditorUtility.DisplayDialog("Lỗi", $"Không thể tạo instance: {ex.Message}", "OK");
            }
        }

        private void CreateNewDataScript()
        {
            string path = EditorUtility.SaveFilePanel(
                "Tạo Data Script Mới",
                "Assets/Dmobin/Monitor/Data/Scripts/Monitors",
                "NewData.cs",
                "cs");

            if (string.IsNullOrEmpty(path)) return;

            // Convert to relative path
            path = path.Replace(Application.dataPath, "Assets");

            // Get namespace and class name
            string className = Path.GetFileNameWithoutExtension(path);
            string namespaceName = "DSDK.Data";

            // Generate script content
            string scriptContent = string.Format(SCRIPT_TEMPLATE, namespaceName, className);

            // Create the script file
            File.WriteAllText(path, scriptContent);

            // Create logic script
            string logicPath = path.Replace(".cs", ".Logic.cs");
            string logicContent = string.Format(SCRIPT_LOGIC_TEMPLATE, namespaceName, className);
            File.WriteAllText(logicPath, logicContent);


            AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);

            // Select the new script
            var asset = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(path);
            Selection.activeObject = asset;
            EditorGUIUtility.PingObject(asset);
        }

        private void FindDataSetting()
        {
            try
            {
                // Cleanup old references
                if (_editor != null) DestroyImmediate(_editor);
                _editor = null;
                _serializedObject = null;
                _dataSetting = null;
                _prefab = null;

                // Nếu đang play thì lấy từ scene
                if (Application.isPlaying)
                {
                    if (DataMonitor.Instance != null)
                    {
                        _dataSetting = DataMonitor.Instance;
                    }
                    else
                    {
                        _prefab = Resources.Load<GameObject>(PREFAB_PATH);
                        if (_prefab != null)
                        {
                            _dataSetting = _prefab.GetComponent<DataMonitor>();
                        }
                    }
                }
                // Không thì lấy từ prefab
                else
                {
                    _prefab = Resources.Load<GameObject>(PREFAB_PATH);
                    if (_prefab != null)
                    {
                        _dataSetting = _prefab.GetComponent<DataMonitor>();
                    }
                }

                if (_dataSetting != null)
                {
                    _serializedObject = new SerializedObject(_dataSetting);
                    _editor = UnityEditor.Editor.CreateEditor(_dataSetting);

                    try
                    {
                        var dataTypes = DataBase.GetAllDataTypes();
                        if (dataTypes != null)
                        {
                            _dataTypes = dataTypes.ToList();
                        }
                        else
                        {
                            _dataTypes = new List<System.Type>();
                            DLogger.LogWarning("DataBase.GetAllDataTypes() trả về null");
                        }
                    }
                    catch (Exception ex)
                    {
                        _dataTypes = new List<System.Type>();
                        DLogger.LogError($"Lỗi khi lấy danh sách data types: {ex.Message}");
                    }
                }
            }
            catch (System.Exception e)
            {
                DLogger.LogError($"[DataSetting] Lỗi khi tìm DataSetting: {e}");
            }
        }

        private void DrawDataSettingInfo()
        {
            using (new EditorGUILayout.VerticalScope(_boxStyle))
            {
                // Draw inspector
                EditorGUI.BeginChangeCheck();
                _serializedObject.Update();

                _editor.OnInspectorGUI();

                if (EditorGUI.EndChangeCheck())
                {
                    _serializedObject.ApplyModifiedProperties();

                    if (!Application.isPlaying)
                    {
                        // Đánh dấu prefab là dirty
                        EditorUtility.SetDirty(_dataSetting);
                        if (_prefab != null)
                        {
                            PrefabUtility.RecordPrefabInstancePropertyModifications(_dataSetting);
                            AssetDatabase.SaveAssets();
                        }
                    }
                    else
                    {
                        _dataSetting = DataMonitor.Instance;
                    }
                }
            }
        }

        private void DrawDataStats()
        {
            if (!Application.isPlaying) return;

            try
            {
                // Kiểm tra xem DataMonitor.Instance có tồn tại không
                if (DataMonitor.Instance == null)
                {
                    EditorGUILayout.HelpBox("DataMonitor.Instance không tồn tại", MessageType.Warning);
                    return;
                }

                var monitorInfo = DataMonitor.Instance.MonitorInfo;
                if (monitorInfo == null)
                {
                    EditorGUILayout.HelpBox("MonitorInfo không tồn tại", MessageType.Warning);
                    return;
                }

                if (monitorInfo.StatsMap == null || monitorInfo.StatsMap.Count == 0)
                {
                    EditorGUILayout.HelpBox("Chưa có dữ liệu thống kê", MessageType.Info);
                    return;
                }

                using (new EditorGUILayout.VerticalScope(_boxStyle))
                {
                    EditorGUILayout.LabelField("Thống Kê Dữ Liệu", _sectionHeaderStyle);
                    EditorGUILayout.Space(5);

                    // Header
                    using (new EditorGUILayout.HorizontalScope(EditorStyles.helpBox))
                    {
                        EditorGUILayout.LabelField("Loại", EditorStyles.boldLabel, GUILayout.Width(150));
                        EditorGUILayout.LabelField("Lưu", EditorStyles.boldLabel, GUILayout.Width(50));
                        EditorGUILayout.LabelField("Tải", EditorStyles.boldLabel, GUILayout.Width(50));
                        EditorGUILayout.LabelField("Truy Cập Cuối", EditorStyles.boldLabel);
                    }

                    // Data rows
                    foreach (var stat in monitorInfo.StatsMap.Values)
                    {
                        using (new EditorGUILayout.HorizontalScope())
                        {
                            EditorGUILayout.LabelField(stat.TypeName, GUILayout.Width(150));
                            EditorGUILayout.LabelField(stat.SaveCount.ToString(), GUILayout.Width(50));
                            EditorGUILayout.LabelField(stat.LoadCount.ToString(), GUILayout.Width(50));
                            EditorGUILayout.LabelField(stat.LastSavedTime ?? "Chưa bao giờ");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                EditorGUILayout.HelpBox($"Lỗi khi hiển thị thống kê: {ex.Message}", MessageType.Error);
            }
        }

        private void DrawFooter()
        {
            // Kiểm tra xem có DataMonitor không (Play mode cần Instance, Edit mode cần prefab)
            if (Application.isPlaying)
            {
                if (DataMonitor.Instance == null)
                {
                    using (new EditorGUILayout.VerticalScope(EditorStyles.helpBox))
                    {
                        EditorGUILayout.HelpBox("DataMonitor Instance không được tìm thấy trong Scene", MessageType.Warning);
                    }
                    return;
                }
            }
            else
            {
                if (_dataSetting == null)
                {
                    using (new EditorGUILayout.VerticalScope(EditorStyles.helpBox))
                    {
                        EditorGUILayout.HelpBox("DataMonitor Prefab không được tìm thấy", MessageType.Warning);
                    }
                    return;
                }
            }

            using (new EditorGUILayout.VerticalScope(_boxStyle))
            {
                EditorGUILayout.LabelField("Thao Tác Dữ Liệu", _sectionHeaderStyle);
                EditorGUILayout.Space(5);

                using (new EditorGUILayout.HorizontalScope())
                {
                    GUILayout.FlexibleSpace();

                    GUI.backgroundColor = new Color(0.3f, 0.7f, 0.3f);
                    if (GUILayout.Button("Tải", _buttonStyle, GUILayout.Width(120)))
                    {
                        if (Application.isPlaying)
                        {
                            DataMonitor.Instance.LoadAllData();
                        }
                        else
                        {
                            LoadDataInEditMode();
                        }
                        EditorApplication.delayCall += RefreshInspector;
                    }

                    GUI.backgroundColor = new Color(0.3f, 0.5f, 0.8f);
                    if (GUILayout.Button("Lưu", _buttonStyle, GUILayout.Width(120)))
                    {
                        if (Application.isPlaying)
                        {
                            DataMonitor.Instance.SaveAllData();
                        }
                        else
                        {
                            SaveDataInEditMode();
                        }
                        EditorApplication.delayCall += RefreshInspector;
                    }

                    GUI.backgroundColor = new Color(0.8f, 0.3f, 0.3f);
                    if (GUILayout.Button("Xóa", _buttonStyle, GUILayout.Width(120)))
                    {
                        if (EditorUtility.DisplayDialog("Xác nhận", "Bạn có chắc muốn xóa tất cả dữ liệu?", "Có", "Không"))
                        {
                            if (Application.isPlaying)
                            {
                                DataMonitor.Instance.DeleteAllData();
                            }
                            else
                            {
                                DeleteDataInEditMode();
                            }
                            EditorApplication.delayCall += RefreshInspector;
                        }
                    }

                    GUI.backgroundColor = Color.white;

                    GUILayout.FlexibleSpace();
                }
            }
        }

        /// <summary>
        /// Load data in Edit mode - update prefab fields directly
        /// </summary>
        private void LoadDataInEditMode()
        {
            try
            {
                if (_dataSetting == null)
                {
                    DLogger.LogError("DataSetting is null in Edit mode", channel: "DataMonitorWindow");
                    return;
                }

                // Setup FileDataHandler with settings from prefab
                var profileId = GetPrivateField<string>(_dataSetting, "_profileId") ?? "main";
                var saveType = GetPrivateField<DataSaveType>(_dataSetting, "_saveType");
                var debug = GetPrivateField<bool>(_dataSetting, "_debug");

                FileDataHandler.Instance.Setup(profileId, Application.persistentDataPath, debug, saveType);

                // Find all data fields and load them
                var dataFields = GetDataFields(_dataSetting);
                int loadedCount = 0;

                foreach (var field in dataFields)
                {
                    try
                    {
                        var fieldType = field.FieldType;
                        var keyProperty = fieldType.GetProperty("Key");

                        if (keyProperty != null)
                        {
                            // Create temporary instance to get key
                            var tempInstance = Activator.CreateInstance(fieldType);
                            var key = keyProperty.GetValue(tempInstance) as string;

                            if (!string.IsNullOrEmpty(key))
                            {
                                // Use reflection to call generic Load method
                                var loadMethod = typeof(FileDataHandler).GetMethod("Load").MakeGenericMethod(fieldType);
                                var loadedData = loadMethod.Invoke(FileDataHandler.Instance, new object[] { key, profileId, false });

                                // Set the loaded data to the field
                                field.SetValue(_dataSetting, loadedData);
                                loadedCount++;

                                DLogger.LogInfo($"Loaded {fieldType.Name} with key {key}", channel: "DataMonitorWindow");
                            }
                        }
                    }
                    catch (Exception fieldEx)
                    {
                        DLogger.LogError($"Error loading field {field.Name}: {fieldEx.Message}", channel: "DataMonitorWindow");
                    }
                }

                DLogger.LogInfo($"Data loaded in Edit mode - {loadedCount} fields processed", channel: "DataMonitorWindow");
            }
            catch (Exception ex)
            {
                DLogger.LogError($"Error loading data in Edit mode: {ex.Message}", channel: "DataMonitorWindow");
            }
        }

        /// <summary>
        /// Save data in Edit mode - save from prefab fields
        /// </summary>
        private void SaveDataInEditMode()
        {
            try
            {
                if (_dataSetting == null)
                {
                    DLogger.LogError("DataSetting is null in Edit mode", channel: "DataMonitorWindow");
                    return;
                }

                // Setup FileDataHandler with settings from prefab
                var profileId = GetPrivateField<string>(_dataSetting, "_profileId") ?? "main";
                var saveType = GetPrivateField<DataSaveType>(_dataSetting, "_saveType");
                var debug = GetPrivateField<bool>(_dataSetting, "_debug");

                FileDataHandler.Instance.Setup(profileId, Application.persistentDataPath, debug, saveType);

                // Find all data fields and save them
                var dataFields = GetDataFields(_dataSetting);
                int savedCount = 0;

                foreach (var field in dataFields)
                {
                    try
                    {
                        var dataInstance = field.GetValue(_dataSetting);

                        if (dataInstance != null)
                        {
                            // Call Save method on the data instance
                            var saveMethod = dataInstance.GetType().GetMethod("Save");
                            if (saveMethod != null)
                            {
                                saveMethod.Invoke(dataInstance, null);
                                savedCount++;

                                DLogger.LogInfo($"Saved {field.FieldType.Name}", channel: "DataMonitorWindow");
                            }
                        }
                    }
                    catch (Exception fieldEx)
                    {
                        DLogger.LogError($"Error saving field {field.Name}: {fieldEx.Message}", channel: "DataMonitorWindow");
                    }
                }

                DLogger.LogInfo($"Data saved in Edit mode - {savedCount} fields processed", channel: "DataMonitorWindow");
            }
            catch (Exception ex)
            {
                DLogger.LogError($"Error saving data in Edit mode: {ex.Message}", channel: "DataMonitorWindow");
            }
        }

        /// <summary>
        /// Delete data in Edit mode - delete saved data and reset prefab fields
        /// </summary>
        private void DeleteDataInEditMode()
        {
            try
            {
                if (_dataSetting == null)
                {
                    DLogger.LogError("DataSetting is null in Edit mode", channel: "DataMonitorWindow");
                    return;
                }

                // Setup FileDataHandler with settings from prefab
                var profileId = GetPrivateField<string>(_dataSetting, "_profileId") ?? "main";
                var saveType = GetPrivateField<DataSaveType>(_dataSetting, "_saveType");
                var debug = GetPrivateField<bool>(_dataSetting, "_debug");

                FileDataHandler.Instance.Setup(profileId, Application.persistentDataPath, debug, saveType);

                // Delete saved data
                FileDataHandler.Instance.DeleteProfile(profileId);

                // Reset prefab fields to default values
                var dataFields = GetDataFields(_dataSetting);
                int resetCount = 0;

                foreach (var field in dataFields)
                {
                    try
                    {
                        // Create new instance of the data type
                        var newInstance = Activator.CreateInstance(field.FieldType);
                        field.SetValue(_dataSetting, newInstance);
                        resetCount++;

                        DLogger.LogInfo($"Reset {field.FieldType.Name} to default", channel: "DataMonitorWindow");
                    }
                    catch (Exception fieldEx)
                    {
                        DLogger.LogError($"Error resetting field {field.Name}: {fieldEx.Message}", channel: "DataMonitorWindow");
                    }
                }

                DLogger.LogInfo($"Data deleted in Edit mode - {resetCount} fields reset", channel: "DataMonitorWindow");
            }
            catch (Exception ex)
            {
                DLogger.LogError($"Error deleting data in Edit mode: {ex.Message}", channel: "DataMonitorWindow");
            }
        }

        /// <summary>
        /// Get all data fields from DataMonitor using reflection
        /// </summary>
        private FieldInfo[] GetDataFields(object dataMonitor)
        {
            var fields = dataMonitor.GetType()
                .GetFields(BindingFlags.NonPublic | BindingFlags.Instance)
                .Where(f => f.FieldType.IsClass &&
                           f.FieldType != typeof(string) &&
                           !f.FieldType.IsEnum &&
                           f.FieldType.GetProperty("Key") != null &&
                           f.Name.StartsWith("_") &&
                           !f.Name.Equals("_profileId") &&
                           !f.Name.Equals("_debug") &&
                           !f.Name.Equals("_saveType") &&
                           !f.Name.Equals("_getDefaultFromMonitor"))
                .ToArray();

            return fields;
        }

        /// <summary>
        /// Get private field value using reflection
        /// </summary>
        private TField GetPrivateField<TField>(object obj, string fieldName)
        {
            var field = obj.GetType().GetField(fieldName, System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (field != null)
            {
                return (TField)field.GetValue(obj);
            }
            return default(TField);
        }

        /// <summary>
        /// Set private field value using reflection
        /// </summary>
        private void SetPrivateField(object obj, string fieldName, object value)
        {
            var field = obj.GetType().GetField(fieldName, System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (field != null)
            {
                field.SetValue(obj, value);
            }
        }

        /// <summary>
        /// Refresh inspector after data operations to show updated values
        /// </summary>
        private void RefreshInspector()
        {
            try
            {
                if (_dataSetting != null && _serializedObject != null)
                {
                    // Refresh all data instances first
                    if (Application.isPlaying && DataMonitor.Instance != null)
                    {
                        DataMonitor.Instance.RefreshAllInstances();
                    }

                    // Update serialized object to reflect changes
                    _serializedObject.Update();

                    // Mark the object as dirty to show changes
                    EditorUtility.SetDirty(_dataSetting);

                    // If we have a prefab reference, record modifications
                    if (_prefab != null && !Application.isPlaying)
                    {
                        PrefabUtility.RecordPrefabInstancePropertyModifications(_dataSetting);
                        AssetDatabase.SaveAssets();
                    }

                    // Force repaint to update UI
                    Repaint();

                    // Refresh scene view if needed
                    SceneView.RepaintAll();

                    DLogger.LogInfo("Inspector refreshed after data operation", channel: "DataMonitorWindow");
                }
            }
            catch (Exception ex)
            {
                DLogger.LogError($"Error refreshing inspector: {ex.Message}");
            }
        }

        private void OnDestroy()
        {
            if (_editor != null)
            {
                DestroyImmediate(_editor);
                _editor = null;
            }
        }
    }
}