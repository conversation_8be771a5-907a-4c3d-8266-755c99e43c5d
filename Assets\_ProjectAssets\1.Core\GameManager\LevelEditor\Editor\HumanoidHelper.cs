using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEngine;

namespace DSDK.LevelEditor
{
    public static class HumanoidHelper
    {
        public static string GetVehicleTypeText(EVehicleType vehicleType)
        {
            switch (vehicleType)
            {
                case EVehicleType.NormalVehicle: return "N";
                case EVehicleType.FireTruck: return "FIRE";
                case EVehicleType.MysteryVehicle: return "M";
                case EVehicleType.Limousine: return "VIP";
                default: return "?";
            }
        }

        public static void DrawLegends()
        {
            EditorGUILayout.LabelField("Legends", EditorStyles.boldLabel);
            GUI.color = Color.yellow;
            EditorGUILayout.BeginHorizontal();
            DrawLegendLine("N", "Normal Vehicle");
            DrawLegendLine("FIRE", "Fire Truck");
            DrawLegendLine("M", "Mystery Vehicle");
            DrawLegendLine("VIP", "Limousine");
            EditorGUILayout.EndHorizontal();
            GUI.color = Color.white;
            EditorGUILayout.Space();
        }

        private static void DrawLegendLine(string label, string description)
        {
            EditorGUILayout.BeginHorizontal();
            GUILayout.Box(label, GUILayout.Width(45), GUILayout.Height(25));
            EditorGUILayout.LabelField(description);
            EditorGUILayout.EndHorizontal();
        }

        public static void FixHumanoidData(LevelSO selectedLevel)
        {
            if (selectedLevel == null)
            {
                Debug.LogError("Selected level is null");
                return;
            }

            // Calculate expected humanoids from vehicles and garages
            List<HumanoidData> expectedHumanoids = GenerateExpectedHumanoids(selectedLevel);

            if (selectedLevel.Humanoids == null)
            {
                selectedLevel.Humanoids = new List<HumanoidData>();
            }

            // Strategy: Minimize changes by reusing existing humanoids where possible
            List<HumanoidData> fixedHumanoids = new List<HumanoidData>();
            List<HumanoidData> availableHumanoids = new List<HumanoidData>(selectedLevel.Humanoids);

            foreach (var expectedHumanoid in expectedHumanoids)
            {
                // Try to find an exact match first (same color and vehicle type)
                var exactMatch = availableHumanoids.FirstOrDefault(h =>
                    h.color == expectedHumanoid.color &&
                    h.targetVehicleType == expectedHumanoid.targetVehicleType);

                if (exactMatch != null)
                {
                    fixedHumanoids.Add(exactMatch);
                    availableHumanoids.Remove(exactMatch);
                    continue;
                }

                // Try to find a match with same color but different vehicle type
                var colorMatch = availableHumanoids.FirstOrDefault(h => h.color == expectedHumanoid.color);
                if (colorMatch != null)
                {
                    // Update the vehicle type to match expected
                    colorMatch.targetVehicleType = expectedHumanoid.targetVehicleType;
                    fixedHumanoids.Add(colorMatch);
                    availableHumanoids.Remove(colorMatch);
                    continue;
                }

                // Try to find a match with same vehicle type but different color
                var typeMatch = availableHumanoids.FirstOrDefault(h => h.targetVehicleType == expectedHumanoid.targetVehicleType);
                if (typeMatch != null)
                {
                    // Update the color to match expected
                    typeMatch.color = expectedHumanoid.color;
                    fixedHumanoids.Add(typeMatch);
                    availableHumanoids.Remove(typeMatch);
                    continue;
                }

                // If no match found, try to reuse any available humanoid
                if (availableHumanoids.Count > 0)
                {
                    var reusableHumanoid = availableHumanoids[0];
                    reusableHumanoid.color = expectedHumanoid.color;
                    reusableHumanoid.targetVehicleType = expectedHumanoid.targetVehicleType;
                    fixedHumanoids.Add(reusableHumanoid);
                    availableHumanoids.RemoveAt(0);
                }
                else
                {
                    // Create new humanoid if no existing ones can be reused
                    fixedHumanoids.Add(new HumanoidData
                    {
                        color = expectedHumanoid.color,
                        targetVehicleType = expectedHumanoid.targetVehicleType
                    });
                }
            }

            // Update the level's humanoid list
            selectedLevel.Humanoids.Clear();
            selectedLevel.Humanoids.AddRange(fixedHumanoids);

            // Mark the asset as dirty for saving
            EditorUtility.SetDirty(selectedLevel);

            Debug.Log($"Fixed humanoid data: {fixedHumanoids.Count} humanoids total. " +
                     $"Reused {selectedLevel.Humanoids.Count - (expectedHumanoids.Count - availableHumanoids.Count)} existing humanoids.");
        }

        private static List<HumanoidData> GenerateExpectedHumanoids(LevelSO selectedLevel)
        {
            List<HumanoidData> expectedHumanoids = new List<HumanoidData>();

            // Generate humanoids for vehicles
            if (selectedLevel.Vehicles != null)
            {
                foreach (var vehicleData in selectedLevel.Vehicles)
                {
                    int humanoidCount = vehicleData.SlotCount; // Using SlotCount instead of Length
                    for (int i = 0; i < humanoidCount; i++)
                    {
                        var humanoidData = new HumanoidData
                        {
                            color = vehicleData.color,
                            targetVehicleType = vehicleData.vehicleType == EVehicleType.MysteryVehicle ?
                                EVehicleType.NormalVehicle : vehicleData.vehicleType
                        };
                        expectedHumanoids.Add(humanoidData);
                    }
                }
            }

            // Generate humanoids for garages
            if (selectedLevel.Garages != null)
            {
                foreach (var garageData in selectedLevel.Garages)
                {
                    int vehicleLength = garageData.VehicleSize.y;
                    if (garageData.VehicleColors != null)
                    {
                        foreach (var vehicleColor in garageData.VehicleColors)
                        {
                            for (int i = 0; i < vehicleLength; i++)
                            {
                                var humanoidData = new HumanoidData
                                {
                                    color = vehicleColor,
                                    targetVehicleType = EVehicleType.NormalVehicle
                                };
                                expectedHumanoids.Add(humanoidData);
                            }
                        }
                    }
                }
            }

            return expectedHumanoids;
        }

        public static int CalculateExpectedHumanoidCount(LevelSO selectedLevel)
        {
            int count = 0;

            // Count humanoids from vehicles
            if (selectedLevel.Vehicles != null)
            {
                foreach (var vehicle in selectedLevel.Vehicles)
                {
                    count += vehicle.SlotCount; // Each vehicle needs Length number of humanoids
                }
            }

            // Count humanoids from garages
            if (selectedLevel.Garages != null)
            {
                foreach (var garage in selectedLevel.Garages)
                {
                    count += garage.TotalSlotCount; // Each garage has VehicleSize.y slots for each color
                }
            }

            return count;
        }

        public static void ValidateHumanoidColorDistribution(List<string> warnings, LevelSO selectedLevel)
        {
            if (selectedLevel.Humanoids == null || selectedLevel.Humanoids.Count == 0) return;

            // Get expected colors from vehicles and garages
            Dictionary<EColor, int> expectedColors = new Dictionary<EColor, int>();
            Dictionary<EColor, int> actualColors = new Dictionary<EColor, int>();
            Dictionary<EVehicleType, int> actualSpecialTypes = new Dictionary<EVehicleType, int>();
            Dictionary<EVehicleType, int> expectedSpecialTypes = new Dictionary<EVehicleType, int>();

            // Count expected colors from vehicles
            if (selectedLevel.Vehicles != null)
            {
                foreach (var vehicle in selectedLevel.Vehicles)
                {
                    if (vehicle.vehicleType == EVehicleType.NormalVehicle || vehicle.vehicleType == EVehicleType.MysteryVehicle)
                    {
                        if (!expectedColors.ContainsKey(vehicle.color))
                        {
                            expectedColors[vehicle.color] = 0;
                        }
                        expectedColors[vehicle.color] += vehicle.SlotCount;
                    }
                    else
                    {
                        if (!expectedSpecialTypes.ContainsKey(vehicle.vehicleType))
                        {
                            expectedSpecialTypes[vehicle.vehicleType] = 0;
                        }
                        expectedSpecialTypes[vehicle.vehicleType] += vehicle.SlotCount;
                    }
                }
            }

            // Count expected colors from garages
            if (selectedLevel.Garages != null)
            {
                foreach (var garage in selectedLevel.Garages)
                {
                    if (garage.VehicleColors != null)
                    {
                        foreach (var color in garage.VehicleColors)
                        {
                            if (!expectedColors.ContainsKey(color))
                                expectedColors[color] = 0;
                            expectedColors[color] += garage.SlotCount; // Each garage has VehicleSize.y slots for each color
                        }
                    }
                }
            }

            // Count actual colors from humanoids
            foreach (var humanoid in selectedLevel.Humanoids)
            {
                if (humanoid.targetVehicleType == EVehicleType.NormalVehicle || humanoid.targetVehicleType == EVehicleType.MysteryVehicle)
                {
                    if (!actualColors.ContainsKey(humanoid.color))
                    {
                        actualColors[humanoid.color] = 0;
                    }
                    actualColors[humanoid.color]++;
                }
                else
                {
                    if (!actualSpecialTypes.ContainsKey(humanoid.targetVehicleType))
                    {
                        actualSpecialTypes[humanoid.targetVehicleType] = 0;
                    }
                    actualSpecialTypes[humanoid.targetVehicleType]++;
                }
            }

            // Compare expected vs actual colors
            foreach (var expectedColor in expectedColors)
            {
                int actualCount = actualColors.ContainsKey(expectedColor.Key) ? actualColors[expectedColor.Key] : 0;
                if (actualCount != expectedColor.Value)
                {
                    warnings.Add($"Color {expectedColor.Key}: Expected {expectedColor.Value} humanoids, found {actualCount}");
                }
            }

            // Check for unexpected colors
            foreach (var actualColor in actualColors)
            {
                if (!expectedColors.ContainsKey(actualColor.Key))
                {
                    warnings.Add($"Unexpected color {actualColor.Key}: Found {actualColor.Value} humanoids, but no vehicles/garages use this color");
                }
            }

            foreach (var expectedSpecialType in expectedSpecialTypes)
            {
                int actualCount = actualSpecialTypes.ContainsKey(expectedSpecialType.Key) ? actualSpecialTypes[expectedSpecialType.Key] : 0;
                if (actualCount != expectedSpecialType.Value)
                {
                    warnings.Add($"Special Vehicle Type {expectedSpecialType.Key}: Expected {expectedSpecialType.Value} humanoids, found {actualCount}");
                }
            }
        }

        public static EColor GetNextSuggestedColor(LevelSO levelSO)
        {
            // Get colors from vehicles and garages
            var usedColors = new HashSet<EColor>();

            if (levelSO.Vehicles != null)
            {
                foreach (var vehicle in levelSO.Vehicles)
                {
                    usedColors.Add(vehicle.color);
                }
            }

            if (levelSO.Garages != null)
            {
                foreach (var garage in levelSO.Garages)
                {
                    if (garage.VehicleColors != null)
                    {
                        foreach (var color in garage.VehicleColors)
                        {
                            usedColors.Add(color);
                        }
                    }
                }
            }

            // Return first used color, or Red as default
            return usedColors.FirstOrDefault();
        }

        public static void AddNewHumanoid(LevelSO levelSO)
        {
            if (levelSO.Humanoids == null)
            {
                levelSO.Humanoids = new List<HumanoidData>();
            }

            // Create new humanoid with smart defaults
            var newHumanoid = new HumanoidData
            {
                color = GetNextSuggestedColor(levelSO),
                targetVehicleType = EVehicleType.NormalVehicle
            };

            levelSO.Humanoids.Add(newHumanoid);
            EditorUtility.SetDirty(levelSO);

            Debug.Log($"Added new humanoid: {newHumanoid.color} {newHumanoid.targetVehicleType}");
        }
    }
}