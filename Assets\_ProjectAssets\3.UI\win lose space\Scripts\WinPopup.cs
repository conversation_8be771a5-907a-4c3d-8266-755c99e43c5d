using DG.Tweening;
using DSDK.Audio;
using DSDK.Core;
using DSDK.Data;
using DSDK.UISystem;
using DSDK.Vibration;
using Spine.Unity;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class WinPopup : UIPanel
{
    [SerializeField] private BaseBtn nextLevelBtn;
    [SerializeField] private TextMeshProUGUI nextLevelText;
    [SerializeField] private BaseBtn multiplierRewardBtn;
    [SerializeField] private TextMeshProUGUI multipleText;
    [SerializeField] private TextMeshProUGUI levelText;
    [SerializeField] private GameObject multiplyWheel;
    [SerializeField] private TextMeshProUGUI rewardCoinText;
    // [SerializeField] private SkeletonGraphic titleSpine;
    [SerializeField] private Animator anim;
    [SerializeField] private WinPopup_NewFeature newFeaturePopup;
    [SerializeField] private SkeletonGraphic big_winHumanoidSpine;
    [SerializeField] private SkeletonGraphic small_winHumanoidSpine;

    [Header("Multiplier Reward")]
    [SerializeField] private Slider pointer;
    [SerializeField] private AnimationCurve animationCurve;
    [SerializeField] private bool canPointer = false;

    private int rewardCoin;
    private float rewardMultiplier = 1f;
    // public bool isHardLevel = false;

    [SerializeField] private float minSpeed = 0.1f;
    [SerializeField] private float maxSpeed = 5f;

    private bool increasing = true;
    private float currentValue = 0f;

    private void Start()
    {
        nextLevelBtn.OnClick(OnNextLevelBtnClick);
        // x2RewardBtn.OnClick(OnX2RewardBtnClick);
        multiplierRewardBtn.OnClick(OnMultiplierRewardBtnClick);
        pointer.onValueChanged.AddListener(OnPointerValueChanged);
    }

    void OnEnable()
    {
        SetData();
        GameManager.Instance.LevelManager.IncreaseLevel();
    }
    public float GetMultipleValue(float value)
    {
        if (value <= 0.1f)
        {
            return 0.5f;
        }
        else if (value <= 0.26f)
        {
            return 1f;
        }
        else if (value <= 0.42f)
        {
            return 1.5f;
        }
        else if (value <= 0.58f)
        {
            return 4f;
        }
        else if (value <= 0.74f)
        {
            return 1.5f;
        }
        else if (value <= 0.9f)
        {
            return 1f;
        }
        else
        {
            return 0.5f;
        }
    }
    public override void Show()
    {
        base.Show();
        // titleSpine.AnimationState.SetAnimation(0, "win1/win1_spawn", false);
        // titleSpine.AnimationState.AddAnimation(0, "win1/win1_idle", true, 0f);
        multiplierRewardBtn.gameObject.SetActive(true);
        AudioServer.I.Pause("Music");
        AudioServer.I.Shot(eAudio.SFX_Win.ToString());
        VibrationManager.Instance.Vibrate(HapticType.Large, 250);

        if (newFeaturePopup.ShowNewFeature())
        {
            big_winHumanoidSpine.gameObject.SetActive(false);
        }
        else
        {
            newFeaturePopup.gameObject.SetActive(false);
            big_winHumanoidSpine.gameObject.SetActive(true);
            big_winHumanoidSpine.AnimationState.SetAnimation(0, "win", false);
            big_winHumanoidSpine.AnimationState.AddAnimation(0, "idle", true, 0f);
        }

        CheckIfCanShowMultiply();
    }
    private void CheckIfCanShowMultiply()
    {
        multiplyWheel.SetActive(GameData.Instance.CurrentLevelIndex > 10);
        multiplierRewardBtn.gameObject.SetActive(GameData.Instance.CurrentLevelIndex > 10);
        small_winHumanoidSpine.gameObject.SetActive(GameData.Instance.CurrentLevelIndex <= 10);

        if (GameData.Instance.CurrentLevelIndex < 10)
        {

            small_winHumanoidSpine.AnimationState.SetAnimation(0, "win", false);
            small_winHumanoidSpine.AnimationState.AddAnimation(0, "idle", true, 0f);
        }
        else
        {
            pointer.value = 0f;
            canPointer = true;
        }



    }


    void Update()
    {
        if (!canPointer) return;
        float speedMultiplier = GetSpeedMultiplier(currentValue);
        float deltaSpeed = speedMultiplier * Time.deltaTime;

        // Cập nhật giá trị
        if (increasing)
        {
            currentValue += deltaSpeed;
            if (currentValue >= 1f)
            {
                currentValue = 1f;
                increasing = false;
            }
        }
        else
        {
            currentValue -= deltaSpeed;
            if (currentValue <= 0f)
            {
                currentValue = 0f;
                increasing = true;
            }
        }


        multipleText.text = (rewardCoin + (rewardCoin * GetMultipleValue(currentValue))).ToString();
        pointer.value = currentValue;
    }

    private float GetSpeedMultiplier(float t)
    {
        // Tính khoảng cách từ giá trị hiện tại đến 0.5
        float distanceFromCenter = Mathf.Abs(t - 0.5f) * 2f; // Normalize về [0,1]

        // Tốc độ cao khi gần center (0.5), thấp khi gần 0 hoặc 1
        float speedFactor = 1f - distanceFromCenter;

        // Lerp giữa min và max speed
        return Mathf.Lerp(minSpeed, maxSpeed, speedFactor);
    }

    public override void Hide()
    {
        AudioServer.I.Resume("Music");
        canPointer = false;
        base.Hide();
    }

    private void OnPointerValueChanged(float value)
    {
        AnimatorStateInfo pointerAnim = anim.GetCurrentAnimatorStateInfo(1);
        anim.Play(pointerAnim.fullPathHash, 1, value);
    }
    public void SetData()
    {
        this.rewardCoin = GameManager.Instance.GameConfigManager.winCoin;
        GameManager.Instance.RewardCoin(rewardCoin);
        nextLevelText.text = rewardCoin.ToString();
        levelText.text = "Level " + GameData.Instance.CurrentLevelIndex + " Completed!";
        rewardCoinText.text = rewardCoin.ToString();
    }
    private void OnMultiplierRewardBtnClick()
    {
        canPointer = false;
        GameManager.Instance.ShowRewardedVideo((isSuccess) =>
        {
            if (isSuccess)
            {
                rewardMultiplier = GetMultipleValue(pointer.value);
                int multipleRewardCoin = Mathf.FloorToInt(rewardCoin * rewardMultiplier);

                GameManager.Instance.RewardCoin(multipleRewardCoin);
                ForceLoadScene();


                Hide();
                GameManager.Instance.HandleWin(multipleRewardCoin);
            }
            else
            {
                canPointer = true;
            }

        }, "multiplier_reward");
    }
    private void ForceLoadScene()
    {
        if (GameData.Instance.CurrentLevelIndex > 5)
        {
            GameManager.Instance.BackToHomeScreen();
        }
        else
        {
            GameManager.Instance.RestartLevel();
        }
    }
    // private void OnX2RewardBtnClick()
    // {
    //     x2RewardBtn.isInteractable = false;
    //     GameManager.Instance.ShowRewardedVideo((isSuccess) =>
    //     {
    //         if (isSuccess)
    //         {
    //             int multipleRewardCoin = rewardCoin;
    //             GameManager.Instance.RewardCoin(multipleRewardCoin);
    //             GameManager.Instance.RestartLevel();
    //             Hide();
    //             GameManager.Instance.HandleWin(multipleRewardCoin);
    //         }
    //         x2RewardBtn.isInteractable = true;
    //     }, "x2_reward");
    // }

    private void OnNextLevelBtnClick()
    {
        ForceLoadScene();
        GameManager.Instance.HandleWin(rewardCoin);
        Hide();
    }
}
