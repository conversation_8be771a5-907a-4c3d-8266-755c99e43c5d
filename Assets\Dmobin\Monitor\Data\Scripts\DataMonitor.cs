using UnityEngine;

namespace DSDK.Data
{
    public class DataMonitor : MonoBehaviour
    {
        #region DONT TOUCH

        private static DataMonitor _instance;

        public static DataMonitor Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType<DataMonitor>();
                }

                return _instance;
            }
        }

        #region Fields

        /// <summary>
        /// <PERSON><PERSON><PERSON> dữ liệu default từ Monitor thay vì lấy từ constructor
        /// </summary>
        [SerializeField] private bool _getDefaultFromMonitor = false;

        [SerializeField] private string _profileId = "main";
        [SerializeField] private bool _debug = false;
        [SerializeField] private DataSaveType _saveType = DataSaveType.PlayerPrefs;
        [HideInInspector] public DataMonitorInfo MonitorInfo;

        public static bool AllDataLoaded = false;

        #endregion

        #region Main Method

        protected void Awake()
        {
            _instance = this;
            FileDataHandler.Instance.Setup(_profileId, Application.persistentDataPath, _debug, _saveType);
            MonitorInfo = DataMonitorInfo.Instance;
            GetInstance();
        }

        #endregion

        #endregion

        // for monitor only ,not use in runtime

        #region Monitor

        #region Monitor Fields
        [SerializeField] private GameData _gamedata;
        [SerializeField] private SettingsData _settingsdata;
        [SerializeField] private AudioData _audiodata;
        [SerializeField] private VibrationData _vibrationdata;
        #endregion

        public void GetInstance()
        {
            if (_getDefaultFromMonitor)
            {
                #region Get Default
                if (!FileDataHandler.Instance.IsExist(_gamedata.Key))
                {
                    GameData.SetInstance(_gamedata);

                    // Save the default data immediately to ensure it persists
                    _gamedata.Save();
                }

                if (!FileDataHandler.Instance.IsExist(_settingsdata.Key))
                {
                    SettingsData.SetInstance(_settingsdata);
                    _settingsdata.Save();
                }

                if (!FileDataHandler.Instance.IsExist(_audiodata.Key))
                {
                    AudioData.SetInstance(_audiodata);
                    _audiodata.Save();
                }

                if (!FileDataHandler.Instance.IsExist(_vibrationdata.Key))
                {
                    VibrationData.SetInstance(_vibrationdata);
                    _vibrationdata.Save();
                }
                #endregion
            }

            RefreshAllInstances();

            AllDataLoaded = true;
        }

        public void LoadAllData()
        {
            #region Load All Data
            GameData.Instance.Load();
            SettingsData.Instance.Load();
            AudioData.Instance.Load();
            VibrationData.Instance.Load();
            #endregion

            RefreshAllInstances();
        }

        /// <summary>
        /// Refresh all data instances to update inspector display
        /// </summary>
        public void RefreshAllInstances()
        {
            #region Refresh Instances
            _gamedata = GameData.Instance;
            _settingsdata = SettingsData.Instance;
            _audiodata = AudioData.Instance;
            _vibrationdata = VibrationData.Instance;
            #endregion
        }

        public void SaveAllData()
        {
            #region Save All Data
            _gamedata.Save();
            _settingsdata.Save();
            _audiodata.Save();
            _vibrationdata.Save();
            #endregion
        }

        public void DeleteAllData()
        {
            FileDataHandler.Instance.DeleteProfile(_profileId);
        }

        #endregion
    }
}