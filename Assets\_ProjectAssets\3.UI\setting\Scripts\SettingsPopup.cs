using DSDK.Audio;
using DSDK.Data;
using DSDK.UISystem;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class SettingsPopup : UIPanel
{
    [SerializeField] private BaseBtn _closeButton;
    [SerializeField] private Toggle _musicBtn;
    [SerializeField] private Toggle _soundBtn;
    [SerializeField] private Toggle _hapticBtn;
    [SerializeField] private BaseBtn _consentBtn;
    [SerializeField] private BaseBtn _replayBtn;
    [SerializeField] private BaseBtn _homeBtn;
    [SerializeField] private TextMeshProUGUI versionText;
    [SerializeField] private GameObject _settingPanel;
    [SerializeField] private SettingsPopup_Confirm _settingsPopupConfirm;

    [SerializeField] private int fullHeight = 1180;
    [SerializeField] private int compactHeight = 949;
    [SerializeField] private RectTransform contentRectTransform;

    protected override void Awake()
    {
        base.Awake();

    }

    void Start()
    {
        _closeButton.OnClick(Close);
        _musicBtn.onValueChanged.AddListener(OnMusicBtnClick);
        _soundBtn.onValueChanged.AddListener(OnSoundBtnClick);
        _hapticBtn.onValueChanged.AddListener(OnHapticBtnClick);
        _consentBtn.OnClick(OnConsentBtnClick);
        _replayBtn.OnClick(OnReplayBtnClick);
        _homeBtn.OnClick(OnHomeBtnClick);

        _settingsPopupConfirm.ActionCancel = () =>
        {
            _settingPanel.SetActive(true);
        };

        // ToggleBtn(_musicBtn, SettingsData.Instance.IsMusicOn);
        // ToggleBtn(_soundBtn, SettingsData.Instance.IsSoundOn);
        // ToggleBtn(_hapticBtn, SettingsData.Instance.IsHapticOn);


        versionText.text = "VERSION " + Application.version;
    }

    public override void Show()
    {
        base.Show();
        GameManager.Instance?.SetGameState(EGameState.Paused);
        AudioServer.I.Pause("Music");
    }

    public void CheckIfIsInMainMenu(bool isInMainMenu)
    {
        _replayBtn.gameObject.SetActive(!isInMainMenu);
        _homeBtn.gameObject.SetActive(!isInMainMenu);
        var sizeDelta = contentRectTransform.sizeDelta;
        sizeDelta.y = isInMainMenu ? compactHeight : fullHeight;
        contentRectTransform.sizeDelta = sizeDelta;
    }

    private void Close()
    {
        Hide();
    }
    public override void Hide()
    {
        base.Hide();
        AudioServer.I.Resume("Music");
        GameManager.Instance?.SetGameState(EGameState.Playing);
    }

    private void OnMusicBtnClick(bool isOn)
    {
        SettingsData.Instance.IsMusicOn = isOn;
        // ToggleBtn(_musicBtn, SettingsData.Instance.IsMusicOn);
    }

    private void OnSoundBtnClick(bool isOn)
    {
        SettingsData.Instance.IsSoundOn = isOn;
        // ToggleBtn(_soundBtn, SettingsData.Instance.IsSoundOn);
    }

    private void OnHapticBtnClick(bool isOn)
    {
        SettingsData.Instance.IsHapticOn = isOn;
        // ToggleBtn(_hapticBtn, SettingsData.Instance.IsHapticOn);
    }

    private void ToggleBtn(Button btn, bool isOn)
    {
        btn.transform.GetChild(0).gameObject.SetActive(!isOn);
        btn.transform.GetChild(1).gameObject.SetActive(isOn);
    }

    private void OnConsentBtnClick()
    {
        MoonSDKManager.Instance.ShowCMP();
    }

    private void OnReplayBtnClick()
    {
        _settingsPopupConfirm.ActionConfirm = () =>
        {
            if (GameData.Instance.CurrentHeart > 0)
            {
                GameManager.Instance.RewardHeart(-1);
                GameManager.Instance.RestartLevel();
            }
            else
            {
                UISystem.Instance.ShowPanel<RefillHeartPopup>().SetForceLoadScene(true);
            }
        };
        _settingPanel.SetActive(false);
        _settingsPopupConfirm.gameObject.SetActive(true);
    }
    private void OnHomeBtnClick()
    {
        _settingsPopupConfirm.ActionConfirm = () =>
        {
            GameManager.Instance.RewardHeart(-1);
            GameManager.Instance.BackToHomeScreen();
        };
        _settingPanel.SetActive(false);
        _settingsPopupConfirm.gameObject.SetActive(true);
    }
}
