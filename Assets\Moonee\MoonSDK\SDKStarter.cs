using AdjustSdk;
using DSDK.Audio;
using DSDK.Data;
using System.Collections;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace Moonee.Moon_SDK
{
    public class SDKStarter : MonoBehaviour
    {
        [SerializeField] private GameObject moonSDK;
        [SerializeField] private GameObject intro;
        [SerializeField] private LoadingOverlay loading;

        private AsyncOperation asyncOperation;

        private void Start()
        {
            intro.gameObject.SetActive(false);
            int indexScene = GameData.I.CurrentLevelIndex == 1 ? 2 : 1;
            asyncOperation = SceneManager.LoadSceneAsync(indexScene);
            asyncOperation.allowSceneActivation = false;

            StartCoroutine(Starter());
        }
        private void InitializeMoonSDK()
        {

            moonSDK.SetActive(true);
            DontDestroyOnLoad(moonSDK);
        }
        private IEnumerator Starter()
        {
            intro.SetActive(true);
            yield return new WaitForSeconds(0.5f);
            RequestATT();
            yield return new WaitForSeconds(3.5f);
            InitializeMoonSDK();
            // intro.SetActive(false);
            StartCoroutine(ShowLoading());

        }
        private IEnumerator ShowLoading()
        {
            loading.Show();
            yield return new WaitForSeconds(3.5f);
            // loading.Hide();
            AudioServer.Instance.PlayMusic(eAudio.SFX_BGM.ToString());
            asyncOperation.allowSceneActivation = true;
        }
        private void RequestATT()
        {
            Adjust.RequestAppTrackingAuthorization((status) =>
            {
                switch (status)
                {
                    case 0:
                        // ATTrackingManagerAuthorizationStatusNotDetermined case
                        break;
                    case 1:
                        // ATTrackingManagerAuthorizationStatusRestricted case
                        break;
                    case 2:
                        // ATTrackingManagerAuthorizationStatusDenied case
                        break;
                    case 3:
                        // ATTrackingManagerAuthorizationStatusAuthorized case
                        break;
                }
            });
        }
    }
}
