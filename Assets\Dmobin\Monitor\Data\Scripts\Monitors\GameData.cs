using System;
using System.Collections.Generic;
using DSDK.Core;
using UnityEngine;

namespace DSDK.Data
{

    [Serializable]
    public partial class GameData : Data<GameData>
    {
        [SerializeField] private bool firstOpen = false;
        [SerializeField] private bool firstPlay = false;
        [SerializeField] private bool removeAd = false;
        [SerializeField] private bool isFirstTimeBackHome = true;
        [SerializeField] private float totalPurchased = 0;

        [SerializeField] private int currentLevelIndex = 1;
        [SerializeField] private int loopLevelIndex = -1;

        [SerializeField] private int currentCoin = 0;
        [SerializeField] private int currentHeart = 5;

        [SerializeField] private int freeHeartEndTime = 0;
        [SerializeField] private int nextHeartRegenerationTime = 0;
        [SerializeField] private bool isFirstTimeShowInterstitial = true;

        [Header("Session Tracking")]
        [SerializeField] private string firstPlayDate;
        [SerializeField] private string lastPlayDate;
        [SerializeField] private int totalPlayDays;
        [SerializeField] private int totalContinuousPlayDays;
        [SerializeField] private int totalSessions;

        public float remainingTime;
        [SerializeField] private int boosterCountShuffle = 0;
        [SerializeField] private int boosterCountHelicopter = 0;
        [SerializeField] private int boosterCountSort = 0;
        [SerializeField] private bool newContentVipShowed = false;
        [SerializeField] private bool newContentFireTruckShowed = false;
        [SerializeField] private bool newContentMysteryShowed = false;
        [SerializeField] private bool newContentGateShowed = false;

        protected override void OnDataLoaded()
        {
            if (string.IsNullOrEmpty(firstPlayDate))
            {
                firstPlayDate = DateTime.Now.ToString("yyyy-MM-dd");
                Save();
            }
        }


        public int FreeHeartRemainTime
        {
            get => freeHeartEndTime - (int)(DateTime.UtcNow.Ticks / TimeSpan.TicksPerSecond);
            set
            {
                freeHeartEndTime = (int)(DateTime.UtcNow.Ticks / TimeSpan.TicksPerSecond) + value;
            }
        }

        public int NextHeartRegenerationTime
        {
            get => nextHeartRegenerationTime;
            set
            {
                nextHeartRegenerationTime = value;
                Save();
            }
        }

        public bool IsAdsRemovePurchased
        {
            get => removeAd;
            set
            {
                removeAd = value;
                Save();
            }
        }

        public int CurrentHeart
        {
            get => currentHeart;
            set
            {
                currentHeart = value;
                if (currentHeart <= 0)
                {
                    currentHeart = 0;
                }
                if (currentHeart > 5)
                {
                    currentHeart = 5;
                }
                Save();
            }
        }

        public int CurrentCoin
        {
            get => currentCoin;
            set
            {
                currentCoin = value;
                Save();
            }
        }

        public int CurrentLevelIndex
        {
            get => currentLevelIndex <= 0 ? 1 : currentLevelIndex;
            set
            {
                currentLevelIndex = value;
                Save();
            }
        }

        public int LoopLevelIndex
        {
            get => loopLevelIndex;
            set
            {
                loopLevelIndex = value;
                Save();
            }
        }

        public bool FirstOpen
        {
            get => firstOpen;
            set
            {
                firstOpen = value;
                Save();
            }
        }

        public bool FirstPlay
        {
            get => firstPlay;
            set
            {
                firstPlay = value;
                Save();
            }
        }

        public bool IsRemoveAd
        {
            get => removeAd;
            set
            {
                removeAd = value;
                Save();
            }
        }

        public int BoosterCountShuffle
        {
            get => boosterCountShuffle;
            set
            {
                boosterCountShuffle = value;
                Save();
            }
        }
        public int BoosterCountHelicopter
        {
            get => boosterCountHelicopter;
            set
            {
                boosterCountHelicopter = value;
                Save();
            }
        }
        public int BoosterCountSort
        {
            get => boosterCountSort;
            set
            {
                boosterCountSort = value;
                Save();
            }
        }
        public bool NewContentVipShowed
        {
            get => newContentVipShowed;
            set
            {
                newContentVipShowed = value;
                Save();
            }
        }
        public bool NewContentFireTruckShowed
        {
            get => newContentFireTruckShowed;
            set
            {
                newContentFireTruckShowed = value;
                Save();
            }
        }
        public bool NewContentMysteryShowed
        {
            get => newContentMysteryShowed;
            set
            {
                newContentMysteryShowed = value;
                Save();
            }
        }
        public bool NewContentGateShowed
        {
            get => newContentGateShowed;
            set
            {
                newContentGateShowed = value;
                Save();
            }
        }

        public int GetBoosterAmount(BoosterType boosterType)
        {
            switch (boosterType)
            {
                case BoosterType.Shuffle:
                    return BoosterCountShuffle;
                case BoosterType.Helicopter:
                    return BoosterCountHelicopter;
                case BoosterType.Sort:
                    return BoosterCountSort;
            }
            return 0;
        }

        public void SetBoosterAmount(BoosterType boosterType, int amount)
        {
            switch (boosterType)
            {
                case BoosterType.Shuffle:
                    BoosterCountShuffle = amount;
                    break;
                case BoosterType.Helicopter:
                    BoosterCountHelicopter = amount;
                    break;
                case BoosterType.Sort:
                    BoosterCountSort = amount;
                    break;
            }
            Save();

        }
        public bool IsNewContentShowed(ENewContent newContent)
        {
            bool isShowed = false;
            switch (newContent)
            {
                case ENewContent.Vip:
                    isShowed = NewContentVipShowed;
                    NewContentVipShowed = true;
                    break;
                case ENewContent.FireTruck:
                    isShowed = NewContentFireTruckShowed;
                    NewContentFireTruckShowed = true;
                    break;
                case ENewContent.MysteryVehicle:
                    isShowed = NewContentMysteryShowed;
                    NewContentMysteryShowed = true;
                    break;
                case ENewContent.Gate:
                    isShowed = NewContentGateShowed;
                    NewContentGateShowed = true;
                    break;
            }
            Save();

            return isShowed;
        }

        public bool IsFirstTimeShowInterstitial
        {
            get => isFirstTimeShowInterstitial;
            set
            {
                isFirstTimeShowInterstitial = value;
                Save();
            }
        }

        public string FirstPlayDate
        {
            get => firstPlayDate;
            set
            {
                firstPlayDate = value;
                Save();
            }
        }


        public int TotalPlayDays
        {
            get => totalPlayDays;
            set
            {
                totalPlayDays = value;
                Save();
            }
        }

        public int TotalContinuousPlayDays
        {
            get => totalContinuousPlayDays;
            set
            {
                totalContinuousPlayDays = value;
                Save();
            }
        }

        public DateTime LastPlayDate
        {
            get
            {
                if (string.IsNullOrEmpty(lastPlayDate))
                {
                    lastPlayDate = DateTime.Now.ToString("yyyy-MM-dd");
                    Save();
                }
                return DateTime.Parse(lastPlayDate);
            }
            set
            {
                lastPlayDate = value.ToString("yyyy-MM-dd");
                Save();
            }
        }

        public int TotalSessions
        {
            get => totalSessions;
            set
            {
                totalSessions = value;
                Save();
            }
        }

        public bool IsFirstTimeBackHome
        {
            get => isFirstTimeBackHome;
            set
            {
                isFirstTimeBackHome = value;
                Save();
            }
        }

        public float TotalPurchased
        {
            get => totalPurchased;
            set
            {
                totalPurchased = value;
                Save();
            }
        }
    }
}