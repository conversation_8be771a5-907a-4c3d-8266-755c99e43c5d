using System;
using DSDK.Core;
using DSDK.Data;
using DSDK.ToastNotification;
using DSDK.UISystem;
using TMPro;
using UnityEngine;
using UnityEngine.SceneManagement;
using System.Linq;
using DSDK.Audio;


#if UNITY_EDITOR
using UnityEditor;
#endif

[System.Serializable]
public enum EGameState
{
    Home,
    Tutorial,
    Playing,
    Waiting, // chờ để select vehicle
    WaitingForBooster, // chờ effect, không thao tác được gì cả
    Paused,
    Loading,
}

public class GameManager : SingletonMonoBehaviour<GameManager>
{
    [SerializeField] private LevelManager levelManager;
    [SerializeField] private EndingManager endingManager;
    [SerializeField] private EGameState currentGameState;
    [SerializeField] private GameConfigManager gameConfigManager;
    [SerializeField] private CameraManager cameraManager;

    [Header("GamePlay")]
    [SerializeField] private PoolInstaller poolInstaller;
    [SerializeField] private GridManager gridManager;
    [SerializeField] private HumanoidFactory humanoidFactory;
    [SerializeField] private ParkingSlotsManager parkingSlotsManager;
    [SerializeField] private Helicopter helicopter;
    [SerializeField] private GameObject tutorialUI;
    [SerializeField] private CustomToast customToast;
    [SerializeField] private BoosterManager boosterManager;



    public HumanoidFactory HumanoidFactory => humanoidFactory;
    public ParkingSlotsManager ParkingSlotsManager => parkingSlotsManager;
    public GridManager GridManager => gridManager;
    public Helicopter Helicopter => helicopter;
    public GameConfigManager GameConfigManager => gameConfigManager;
    public LevelManager LevelManager => levelManager;
    public EGameState CurrentGameState => currentGameState;
    public ColorManager ColorManager => levelManager.ColorManager;
    public CameraManager CameraManager => cameraManager;
    public bool CanDetectInput => currentGameState == EGameState.Playing || currentGameState == EGameState.Waiting || currentGameState == EGameState.Tutorial;

    public EGameState _previousGameState;

    private int _moves;
    private int _coinsSpent;


    protected override void Awake()
    {
        Application.targetFrameRate = 60;
    }

    void OnEnable()
    {
        this.AddEventListener(EventID.OnLoadComplete, OnLoadComplete);
    }

    void OnDisable()
    {
        this.RemoveEventListener(EventID.OnLoadComplete, OnLoadComplete);
    }

    void Start()
    {
        if (levelManager == null) return;
        OnLoadComplete();

    }
    private void OnLoadComplete()
    {


        // AudioServer.I.PlayMusic(eAudio.SFX_BGM.ToString());

        // Scene loadedScene = SceneManager.GetSceneByName(gameSceneManager.GameplaySceneName);
        levelManager.Load(true);
        InitLevel(levelManager.LevelData);
        AdWrapper.Instance.ShowBanner();

    }

    public void InitLevel(LevelSO levelData)
    {
        Debug.Log("Init GamePlayManager");

        humanoidFactory.InitializeFactory(levelData);
        parkingSlotsManager.Initialize(humanoidFactory);
        _moves = 0;
        TrackingWrapper.Instance.StartLevel(GameData.I.CurrentLevelIndex, GameData.I.CurrentCoin);
    }
    private void Update()
    {
#if UNITY_EDITOR
        if (Input.GetKeyDown(KeyCode.W))
        {
            this.DispatchEvent(EventID.OnWin);
        }
        if (Input.GetKeyDown(KeyCode.R))
        {
            RestartLevel();
        }

        if (Input.GetKeyDown(KeyCode.Q))
        {
            PreviousLevel();
        }

        if (Input.GetKeyDown(KeyCode.E))
        {
            NextLevel();
        }

        if (Input.GetKeyDown(KeyCode.P))
        {
            QuickProduceHumanoids();
        }
#endif
    }

    private void QuickProduceHumanoids()
    {
        levelManager.ProduceHumanoids(levelManager.LevelData);

        RestartLevel();
    }
    public void GoToLevel(int levelIndex)
    {
        if (levelIndex < 1)
        {
            GameData.I.CurrentLevelIndex = 1;
        }
        else if (levelIndex > levelManager.levelsCount)
        {
            GameData.I.CurrentLevelIndex = levelManager.levelsCount;
        }
        GameData.I.CurrentLevelIndex = levelIndex;

        ReloadScene();
    }
    public void PreviousLevel()
    {
        GameData.I.CurrentLevelIndex--;
        if (GameData.I.CurrentLevelIndex <= 0)
        {
            GameData.I.CurrentLevelIndex = levelManager.levelsCount;
        }

        ReloadScene();
    }

    public void NextLevel()
    {
        GameData.I.CurrentLevelIndex++;
        if (GameData.I.CurrentLevelIndex > levelManager.levelsCount)
        {
            GameData.I.CurrentLevelIndex = 1;
        }

        ReloadScene();
    }


    public void RestartLevel(bool isLoseHeart = false)
    {
        if (isLoseHeart)
        {
            GameData.I.CurrentHeart--;
        }
        ReloadScene();
    }
    public void BackToHomeScreen()
    {
        if (GameData.I.IsFirstTimeBackHome && !MoonSDKManager.I.IsD0)
        {
            HomeScreenManager.IsShowOffer = true;
            HomeScreenManager.ShowOfferType = HomeScreenManager.OfferType.FirstTimeBackHome;
        }
        SceneManager.LoadScene(SceneManager.GetActiveScene().buildIndex - 1);
    }
    private void ReloadScene()
    {
        SceneManager.LoadScene(SceneManager.GetActiveScene().name);
    }

    public void SetGameState(EGameState gameState)
    {
        Debug.Log($"SetGameState: {gameState}");
        currentGameState = gameState;
    }

    public void RewardCoin(int amount)
    {
        GameData.I.CurrentCoin += amount;
        this.DispatchEvent(EventID.OnCoinChange);
    }
    public void RewardHeart(int amount)
    {
        GameData.I.CurrentHeart += amount;
        this.DispatchEvent(EventID.OnHeartCountChange);
    }
    public void ModifyBoosterAmount(BoosterType boosterType, int amount)
    {
        int totalAmount = GameData.I.GetBoosterAmount(boosterType) + amount;
        GameData.I.SetBoosterAmount(boosterType, totalAmount);
        this.DispatchEvent(EventID.OnBoosterAmountChanged, boosterType);
    }
    public void IncrementMoves()
    {
        _moves++;
    }

    /// <summary>
    /// Lấy số lượng xe còn lại trong level
    /// </summary>
    /// <returns>Số lượng xe còn lại</returns>
    public void BuyBooster(BoosterData boosterData, int amount)
    {
        var boosterType = boosterData.type;
        var price = boosterData.price * amount;
        if (price > GameData.I.CurrentCoin)
        {
            Debug.LogError("Not enough money");
            ShowErrorToast("Not enough money");
            return;
        }

        if (GainBooster(boosterType, amount))
        {
            RewardCoin(-price);
            _coinsSpent += price;
            this.DispatchEvent(EventID.OnBoosterAmountChanged, boosterType);
            ShowSuccessToast("Booster bought successfully");
        }
        else
        {
            Debug.LogError("GainBooster failed");
        }
    }

    private bool GainBooster(BoosterType boosterType, int amount)
    {
        switch (boosterType)
        {
            case BoosterType.Helicopter:
                ModifyBoosterAmount(BoosterType.Helicopter, amount);
                return true;
            case BoosterType.Shuffle:
                ModifyBoosterAmount(BoosterType.Shuffle, amount);
                return true;
            case BoosterType.Sort:
                ModifyBoosterAmount(BoosterType.Sort, amount);
                return true;
        }

        return false;
    }

    public void ActiveBooster(BoosterType boosterType)
    {
        if (!CanActiveBooster(boosterType))
        {
            return;
        }

        switch (boosterType)
        {
            case BoosterType.Shuffle:
                HandleBoosterShuffleActive();
                break;
            case BoosterType.Helicopter:
                HandleBoosterHelicopterActive();
                break;
            case BoosterType.Sort:
                HandleBoosterSortActive();
                break;
        }
        this.DispatchEvent(EventID.OnBoosterActive);
    }

    public ParkingSlot FindAvailableSlot(Transform vehicleTransform, out Vector3[] path)
    {
        var slot = HumanoidFactory.ParkingSlotManager.GetAvailableSlot();
        if (slot == null)
        {
            path = null;
            return null;
        }
        path = HumanoidFactory.ParkingSlotManager.GenerateDirectPathToParkingSlot(vehicleTransform, slot);
        return slot;
    }

    public bool ReviveByCoin()
    {
        if (GameData.I.CurrentCoin < GameConfigManager.RevivePrice)
        {
            ShowErrorToast("Not enough money");
            return false;
        }

        RewardCoin(-GameConfigManager.RevivePrice);
        _coinsSpent += GameConfigManager.RevivePrice;
        Revive();
        return true;
    }

    public void Revive()
    {
        Debug.Log("Revive");
        parkingSlotsManager.UnlockAlockedSlot();
        SetGameState(EGameState.Playing);
    }

    private void HandleBoosterSortActive()
    {
        if (currentGameState == EGameState.WaitingForBooster)
        {
            return;
        }

        currentGameState = EGameState.WaitingForBooster;

        void Finish()
        {
            ModifyBoosterAmount(BoosterType.Sort, -1);
            this.DispatchEvent(EventID.OnBoosterAmountChanged, BoosterType.Sort);
            currentGameState = EGameState.Playing;
        }


        currentGameState = EGameState.Waiting;
        Vehicle[] vehicles = ParkingSlotsManager.GetAllVehicles();
        HumanoidFactory.SortHumanoids(vehicles);
        Finish();
    }

    private void HandleBoosterHelicopterActive()
    {
        if (!CanActiveBooster(BoosterType.Helicopter))
        {
            return;
        }

        currentGameState = EGameState.Waiting;
        this.AddEventListener<EventID, Vehicle>(EventID.OnVehicleSelected, OnSelected);
        UISystem.Instance.ShowPanel<UsingHelicopterOverlay>();
    }

    public void CancelBoosterHelicopter()
    {
        currentGameState = EGameState.Playing;
        this.RemoveEventListener<EventID, Vehicle>(EventID.OnVehicleSelected, OnSelected);
    }

    void OnSelected(Vehicle vehicle)
    {
        Debug.Log($"OnSelected {vehicle} - active helicopter");
        UISystem.Instance.HidePanel<UsingHelicopterOverlay>();
        ActiveHelicopter(vehicle, () =>
        {
            currentGameState = EGameState.Playing;
            vehicle.HandleFliedToParkingSlot();
            ModifyBoosterAmount(BoosterType.Helicopter, -1);
            this.DispatchEvent(EventID.OnBoosterAmountChanged, BoosterType.Helicopter);
        });
    }

    private void HandleBoosterShuffleActive()
    {
        if (currentGameState == EGameState.WaitingForBooster)
        {
            return;
        }

        currentGameState = EGameState.WaitingForBooster;

        void Finish(bool success = false)
        {
            currentGameState = EGameState.Playing;
            if (success)
            {
                ModifyBoosterAmount(BoosterType.Shuffle, -1);
            }

            this.DispatchEvent(EventID.OnBoosterAmountChanged, BoosterType.Shuffle);
            Debug.Log($"Finish {currentGameState}");
        }

        void Animate(Vehicle[] vehicles)
        {
            foreach (var v in vehicles)
            {
                v.Animate();
            }
        }
        var allVehicles = levelManager.GetNonParkingVehicles();
        // Get all non-parking vehicles for shuffling
        if (allVehicles.Length < 2)
        {
            ShowErrorToast("Not enough vehicles to shuffle");
            Finish();
            return;
        }

        void ShuffleVehicles()
        {

            // Create a list of vehicles to shuffle
            var vehiclesToShuffle = allVehicles.Where(v => v.CanSwap()).ToList();

            // Shuffle each vehicle with a random compatible vehicle
            for (int i = 0; i < vehiclesToShuffle.Count; i++)
            {
                var currentVehicle = vehiclesToShuffle[i];

                // Find a random compatible vehicle (different from current)
                var compatibleVehicles = vehiclesToShuffle
                    .Where(v => v != currentVehicle && currentVehicle.CanSwap(v))
                    .ToList();

                if (compatibleVehicles.Count > 0)
                {
                    // Pick a random compatible vehicle
                    var randomCompatibleVehicle = compatibleVehicles[UnityEngine.Random.Range(0, compatibleVehicles.Count)];
                    currentVehicle.Swap(randomCompatibleVehicle);
                }
            }
        }

        var count = 0;

        // Blink all vehicles first
        foreach (var v in allVehicles)
        {
            v.Blink(GameManager.I.ColorManager.blinkDuration, () =>
            {
                count++;
                if (count >= allVehicles.Length)
                {
                    ShuffleVehicles();
                    Finish(true);
                    Animate(allVehicles);
                }
            });
        }
    }

    public void ActiveHelicopter(Vehicle vehicle, Action onComplete = null)
    {
        if (currentGameState == EGameState.WaitingForBooster)
        {
            return;
        }

        currentGameState = EGameState.WaitingForBooster;
        var slot = HumanoidFactory.ParkingSlotManager.GetVipSlot();
        vehicle.SetTargetParkingSlot(slot);
        slot.ParkVehicle(vehicle);
        slot.UnlockVipSlot();
        AudioServer.I.Shot(eAudio.SFX_booster_helicopter.ToString());
        helicopter.Pickup(vehicle, slot, onComplete);
    }

    public bool CanActiveBooster(BoosterType boosterType)
    {
        var amount = GetBoosterAmount(boosterType);
        if (amount == 0)
        {
            ShowErrorToast("Not enough booster");
            return false;
        }

        switch (boosterType)
        {
            case BoosterType.Helicopter:
                if (levelManager.RemainVehicleCount < 1)
                {
                    ShowErrorToast("Not enough vehicle to use helicopter");
                    return false;
                }

                if (!ParkingSlotsManager.GetVipSlot().IsEmpty)
                {
                    ShowErrorToast("Parking slot is occupied");
                    return false;
                }
                break;
            case BoosterType.Shuffle:
                if (levelManager.RemainVehicleCount < 3)
                {
                    ShowErrorToast("Not enough vehicle to shuffle");
                    return false;
                }

                break;
            case BoosterType.Sort:
                if (!ParkingSlotsManager.IsHaveVehicle())
                {
                    ShowErrorToast("There is no vehicle in parking slot");
                    return false;
                }
                break;
        }

        return true;
    }

    public bool ViewRewardForBooster(BoosterType boosterType)
    {
        if (!AdWrapper.Instance.IsRewardedVideoReady())
        {
            ShowErrorToast("Rewarded video is not ready");
            return false;
        }

        AdWrapper.Instance.ShowRewardedVideo((isSuccess) =>
        {
            if (isSuccess)
            {
                GainBooster(boosterType, 1);
                ShowSuccessToast("Booster gained successfully");
                this.DispatchEvent(EventID.OnBoosterAmountChanged, boosterType);
            }
            else
            {
                ShowErrorToast("Rewarded video is not available");
            }
        }, "gain_booster", GameData.I.CurrentLevelIndex);

        return true;
    }

    public int GetCurrentLevel()
    {
        return GameData.I.CurrentLevelIndex;
    }

    public bool CanClaimBooster(BoosterData boosterData)
    {
        return GetBoosterAmount(boosterData.type) == 0 && boosterData.level == GetCurrentLevel();
    }

    public int GetBoosterAmount(BoosterType boosterType)
    {
        switch (boosterType)
        {
            case BoosterType.Helicopter:
                return GameData.I.GetBoosterAmount(BoosterType.Helicopter);
            case BoosterType.Shuffle:
                return GameData.I.GetBoosterAmount(BoosterType.Shuffle);
            case BoosterType.Sort:
                return GameData.I.GetBoosterAmount(BoosterType.Sort);
        }

        return 0;
    }

    public void ReceiveSponsorBooster(BoosterType boosterType, int amount)
    {
        if (GetBoosterAmount(boosterType) > 0)
        {
            return;
        }

        switch (boosterType)
        {
            case BoosterType.Helicopter:
                GameData.I.SetBoosterAmount(BoosterType.Helicopter, amount);
                break;
            case BoosterType.Shuffle:
                GameData.I.SetBoosterAmount(BoosterType.Shuffle, amount);
                break;
            case BoosterType.Sort:
                GameData.I.SetBoosterAmount(BoosterType.Sort, amount);
                break;
        }

        this.DispatchEvent(EventID.OnBoosterAmountChanged, boosterType);
    }

    public void SetTutorial(TutorialObject tutorialObject)
    {
        void OnTap(Vehicle vehicle)
        {
            if (vehicle != tutorialObject.vehicle)
            {
                Debug.LogError("Tapped vehicle is not the tutorial vehicle");
                return;
            }

            this.RemoveEventListener<EventID, Vehicle>(EventID.OnVehicleSelected, OnTap);
            tutorialObject.gameObject.SetActive(false);
            tutorialUI.SetActive(false);
            currentGameState = EGameState.Playing;
            tutorialObject.vehicle.HandleTouchInput();
        }

        currentGameState = EGameState.Tutorial;
        tutorialUI.SetActive(true);
        this.AddEventListener<EventID, Vehicle>(EventID.OnVehicleSelected, OnTap);
    }

    public void Pause()
    {
        if (currentGameState == EGameState.Paused)
        {
            return;
        }

        _previousGameState = currentGameState;
        currentGameState = EGameState.Paused;
    }

    public void Resume()
    {
        if (_previousGameState == EGameState.Paused)
        {
            currentGameState = EGameState.Playing;
        }
        else
        {
            currentGameState = _previousGameState;
        }
    }

    public int GetCurrentCoin()
    {
        return GameData.I.CurrentCoin;
    }

    public void ShowRewardedVideo(Action<bool> callback, string name = "")
    {
        if (!AdWrapper.Instance.IsRewardedVideoReady())
        {
            ShowErrorToast("Rewarded video is not ready");
            callback?.Invoke(false);
            return;
        }

        AdWrapper.Instance.ShowRewardedVideo((isSuccess) =>
        {
            if (isSuccess)
            {
                callback?.Invoke(true);
            }
            else
            {
                ShowErrorToast("Rewarded video is not available");
                callback?.Invoke(false);
            }
        }, name, GameData.I.CurrentLevelIndex);
    }

    public void HandleWin(int rewardCoin)
    {
        TrackingWrapper.Instance.EndLevel(GameData.I.CurrentLevelIndex, true, _moves, GameData.I.CurrentCoin, rewardCoin, _coinsSpent);
    }

    public void HandleLose()
    {
        TrackingWrapper.Instance.EndLevel(GameData.I.CurrentLevelIndex, false, _moves, GameData.I.CurrentCoin, 0, _coinsSpent);
    }

    public void ShowCustomToast()
    {
        customToast.Show();
    }

    public void ShowErrorToast(string message)
    {
        ToastManager.Instance.Show(new string[] { message }, ToastManager.ToastItemType.Error,
            ToastManager.ToastMode.Normal);
    }

    public void ShowSuccessToast(string message)
    {
        ToastManager.Instance.Show(new string[] { message }, ToastManager.ToastItemType.Success,
            ToastManager.ToastMode.Normal);
    }
}