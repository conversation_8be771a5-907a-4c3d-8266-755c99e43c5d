using System;
using System.Collections.Generic;
using System.Linq;
using DSDK.Core;
using DSDK.Data;
using Newtonsoft.Json;
using UnityEngine;
using UnityEngine.Purchasing;
using UnityEngine.Purchasing.Extension;

public class IAPManager : SingletonMonoBehaviour<IAPManager>, IDetailedStoreListener
{
    private IAPConfig iapConfig => RemoteConfigWrapper.I.GetIAPConfig();
    public bool IsInitialized => _storeController != null && _storeExtensionProvider != null;
    public List<IAPProduct> products = new();
    private IStoreController _storeController;
    private IExtensionProvider _storeExtensionProvider;

    public Action<bool> OnIAPInitialized;
    public Action<string, string> OnPurchaseFailedEvent;
    public Action<string, bool> OnCheckProductAvailable;
    public Action<string, bool> OnCheckProductOwned;
    public Action<string> OnPurchaseSucceeded;
    public Action<bool> OnRestorePurchases;
    public bool IsRestoringPurchases = false;

    private void Start()
    {
        base.Awake();
        if (RemoteConfigWrapper.I.IsFetched)
        {
            InitializeIAP();
        }
        else
        {
            RemoteConfigWrapper.I.OnRemoteConfigFetched += InitializeIAP;
        }
    }

    void InitializeIAP()
    {
        // Initialize IAP products
        foreach (var product in iapConfig.IapProducts)
        {
            products.Add(product.Value);
        }

        var builder = ConfigurationBuilder.Instance(StandardPurchasingModule.Instance());
        foreach (var product in products)
        {
            if (string.IsNullOrEmpty(product.ProductId)) continue;

            builder.AddProduct(product.ProductId,
                GetProductType(product.type),
                new IDs
                {
                        { product.ProductId, AppleAppStore.Name },
                        { product.ProductId, GooglePlay.Name }
                }
            );
        }
        UnityPurchasing.Initialize(this, builder);
    }

    #region Purchase Methods
    public void PurchaseProduct(string productId)
    {
        if (!IsInitialized)
        {
            Debug.LogError("[DSDK] IAP not initialized");
            return;
        }

        var product = _storeController.products.WithID(productId);
        if (product == null || !product.availableToPurchase)
        {
            Debug.LogError($"[DSDK] Product {productId} is not available for purchase");
            OnPurchaseFailedEvent?.Invoke(productId, "Product not available");
            return;
        }

        Debug.Log($"[DSDK] Purchasing product: {productId}");
        _storeController.InitiatePurchase(product);
    }

    public void RestorePurchases()
    {
        if (!IsInitialized)
        {
            return;
        }

        if (Application.platform == RuntimePlatform.IPhonePlayer || Application.platform == RuntimePlatform.OSXPlayer)
        {
            var apple = _storeExtensionProvider.GetExtension<IAppleExtensions>();
            apple.RestoreTransactions((result, message) =>
            {
                OnRestorePurchases?.Invoke(result);
                IsRestoringPurchases = false;
            });
        }
        else if (Application.platform == RuntimePlatform.Android)
        {
            IsRestoringPurchases = true;
            var google = _storeExtensionProvider.GetExtension<IGooglePlayStoreExtensions>();
            google.RestoreTransactions((result, message) =>
            {
                IsRestoringPurchases = false;
            });
        }
        else
        {
            Debug.LogError("[DSDK] Restoring purchases not supported on this platform");
        }
    }
    #endregion

    #region Get IAP Products
    private ProductType GetProductType(IAPProductType productType)
    {
        switch (productType)
        {
            case IAPProductType.Consumable:
                return ProductType.Consumable;
            case IAPProductType.NonConsumable:
                return ProductType.NonConsumable;
            case IAPProductType.Subscription:
                return ProductType.Subscription;
        }

        return ProductType.NonConsumable;
    }

    public Product GetProductById(string productId)
    {
        return IsInitialized ? _storeController.products.WithID(productId) : null;
    }

    public string GetProductPrice(string productId)
    {
        var product = IsInitialized ? _storeController.products.WithID(productId) : null;
        return product?.metadata.localizedPriceString ?? "N/A";
    }

    public bool IsProductOwned(string productId)
    {
        if (!IsInitialized) return false;

        var product = IsInitialized ? _storeController.products.WithID(productId) : null;
        if (product == null) return false;

        // For non-consumable products, check if they have been purchased
        return (product.definition.type == ProductType.NonConsumable || product.definition.type == ProductType.Subscription) && product.hasReceipt;
    }
    #endregion

    #region IDetailedStoreListener implementation

    public void OnInitialized(IStoreController controller, IExtensionProvider extensions)
    {
        _storeController = controller;
        _storeExtensionProvider = extensions;

        // Log available products
        foreach (var product in controller.products.all)
        {
            if (product.availableToPurchase)
            {
                OnCheckProductAvailable?.Invoke(product.definition.id, true);
                Debug.Log($"[IAP] Product available: {product.definition.id}, {product.metadata.localizedPrice} {product.metadata.isoCurrencyCode}");
            }
            else
            {
                OnCheckProductAvailable?.Invoke(product.definition.id, false);
                Debug.Log($"[IAP] Product not available: {product.definition.id}");
            }

            if (product.hasReceipt && (product.definition.type == ProductType.NonConsumable || product.definition.type == ProductType.Subscription))
            {
                OnCheckProductOwned?.Invoke(product.definition.id, true);
                Debug.Log($"[IAP] Product owned: {product.definition.id}");
            }
            else
            {
                OnCheckProductOwned?.Invoke(product.definition.id, false);
                Debug.Log($"[IAP] Product not owned: {product.definition.id}");
            }
        }

        OnIAPInitialized?.Invoke(true);
    }

    public void OnInitializeFailed(InitializationFailureReason error)
    {
        Debug.LogError($"[IAP] Initialization failed: {error}");
        OnIAPInitialized?.Invoke(false);
    }

    public void OnInitializeFailed(InitializationFailureReason error, string message)
    {
        Debug.LogError($"[IAP] Initialization failed: {error}, Message: {message}");
        OnIAPInitialized?.Invoke(false);
    }

    public PurchaseProcessingResult ProcessPurchase(PurchaseEventArgs purchaseEvent)
    {

        var product = products.FirstOrDefault(p => p.ProductId == purchaseEvent.purchasedProduct.definition.id);
        if (product != null)
        {
            Debug.Log($"[DSDK] Purchase succeeded: {product.ProductId}");
            if (OnPurchaseSucceeded != null)
            {
                OnPurchaseSucceeded(product.ProductId);
                GameData.I.TotalPurchased += (float)purchaseEvent.purchasedProduct.metadata.localizedPrice;
            }
            else
            {
                Debug.LogWarning($"[DSDK] OnPurchaseSucceeded is not set for product: {product.ProductId}");
            }

            return PurchaseProcessingResult.Complete;
        }

        Debug.LogWarning($"[DSDK] Purchase not found in product list: {purchaseEvent.purchasedProduct.definition.id}");
        TrackingManagerWrapper.Instance.TrackIAPEvent(purchaseEvent.purchasedProduct.definition.id, IapState.Failed, "Purchase not found in product list");
        return PurchaseProcessingResult.Complete;
    }

    public void OnPurchaseFailed(Product product, PurchaseFailureReason failureReason)
    {
        OnPurchaseFailedEvent?.Invoke(product.definition.id, failureReason.ToString());
    }

    public void OnPurchaseFailed(Product product, PurchaseFailureDescription failureDescription)
    {
        OnPurchaseFailedEvent?.Invoke(product.definition.id, failureDescription.message);
    }
    #endregion
}


[Serializable]
public class IAPProduct
{
    [JsonProperty("Name")]
    public string Name { get; set; }

    [JsonProperty("ProductId")]
    public string ProductId { get; set; }
    [JsonProperty("IAPProductType")]
    public IAPProductType type;
}

[Serializable]
public class IAPConfig
{
    [JsonProperty("IapProducts")]
    public Dictionary<string, IAPProduct> IapProducts { get; set; }
}

[Serializable]
public enum IAPProductType
{
    Consumable,
    NonConsumable,
    Subscription
}