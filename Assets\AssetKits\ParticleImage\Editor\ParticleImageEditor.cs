// Version: 1.2.0
#if UNITY_BURST && UNITY_MATHEMATICS && UNITY_COLLECTIONS
#define PARTICLE_IMAGE_JOBS
#endif

using AssetKits.ParticleImage.Enumerations;
using UnityEditor;
using UnityEditor.Experimental.SceneManagement;
using UnityEditor.SceneManagement;
using UnityEngine;
using PlayMode = AssetKits.ParticleImage.Enumerations.PlayMode;

namespace AssetKits.ParticleImage.Editor
{
    [CanEditMultipleObjects]
    [CustomEditor(typeof(ParticleImage))]
    public class ParticleImageEditor : UnityEditor.Editor
    {
        private ParticleImage _particle;
        
        //General Properties
        private SerializedProperty _space;
        private SerializedProperty _timeScale;
        private SerializedProperty _playMode;
        private SerializedProperty _loop;
        private SerializedProperty _prewarm;
        private SerializedProperty _duration;
        private SerializedProperty _delay;
        private SerializedProperty _life;
        private SerializedProperty _speed;
        private SerializedProperty _startSize;
        private SerializedProperty _startRotation;
        private SerializedProperty _startColor;
        private SerializedProperty _raycast;
        private SerializedProperty _maskable;
        
        //Emitter Properties
        private SerializedProperty _rate;
        private SerializedProperty _rateOverLifetime;
        private SerializedProperty _rateOverDistance;
        private SerializedProperty _burst;
        private SerializedProperty _emitterShape;
        private SerializedProperty _width;
        private SerializedProperty _height;
        private SerializedProperty _length;
        private SerializedProperty _radius;
        private SerializedProperty _angle;
        private SerializedProperty _surface;
        private SerializedProperty _edge;
        private SerializedProperty _spreadType;
        private SerializedProperty _spreadLoop;
        private SerializedProperty _startPoint;
        private SerializedProperty _startPointTrans;
        
        //Particle Properties
        private SerializedProperty _sprite;
        private SerializedProperty _texture;
        private SerializedProperty _material;
        private SerializedProperty _speedOverLifetime;
        private SerializedProperty _sizeOverLifetime;
        private SerializedProperty _sizeBySpeed;
        private SerializedProperty _sizeSpeedRange;
        private SerializedProperty _colorOverLifetime;
        private SerializedProperty _colorBySpeed;
        private SerializedProperty _colorSpeedRange;
        private SerializedProperty _rotateOverLifetime;
        private SerializedProperty _rotationBySpeed;
        private SerializedProperty _rotationSpeedRange;
        private SerializedProperty _alignDirection;
        private SerializedProperty _sheetModule;
        private SerializedProperty _sheetType;
        private SerializedProperty _tile;
        private SerializedProperty _frameOverTime;
        private SerializedProperty _startFrame;
        private SerializedProperty _frameSpeedRange;
        private SerializedProperty _frameFps;
        private SerializedProperty _cycles;
        
        private SerializedProperty _trailModule;
        private SerializedProperty _trailWidth;
        private SerializedProperty _trailColorOverLifetime;
        private SerializedProperty _trailColorOverTrail;
        private SerializedProperty _trailMaterial;
        private SerializedProperty _trailLifetime;
        private SerializedProperty _inheritColor;
        private SerializedProperty _dieWithParticle;
        private SerializedProperty _trailRatio;
        private SerializedProperty _minimumVertexDistance;
        
        //Movement Properties
        private SerializedProperty _targetModule;
        private SerializedProperty _targetTransform;
        private SerializedProperty _targetCurve;
        private SerializedProperty _targetMode;
        
        private SerializedProperty _noiseModule;
        private SerializedProperty _noiseFreq;
        private SerializedProperty _noiseOffset;
        private SerializedProperty _noiseStrength;
        
        private SerializedProperty _velocityModule;
        private SerializedProperty _velocitySpace;
        private SerializedProperty _velocityOverLifetime;
        
        private SerializedProperty _gravityModule;
        private SerializedProperty _gravity;
        
        private SerializedProperty _vortexModule;
        private SerializedProperty _vortexStrength;
        
        //Event Properties
        private SerializedProperty _onStart;
        private SerializedProperty _onFinish;
        private SerializedProperty _onFirstParticleFinish;
        private SerializedProperty _onLastParticleFinish;
        private SerializedProperty _onParticleFinish;
        
        //Advanced Properties
        private SerializedProperty _multithreadModule;
        private SerializedProperty _multithreadEnabled;
        private bool hasCollections;
        private bool hasMathematics;
        private bool hasBurst;

        //Editor Icons
        private Texture _particleModuleIcon;
        private Texture _movementModuleIcon;
        private Texture _emitterModuleIcon;
        private Texture _eventModuleIcon;
        private Texture _advancedModuleIcon;
        
        //Module Foldouts
        private GUIStyle _foldoutStyle;
        private GUIStyle FoldoutStyle
        {
            get
            {
                if (_foldoutStyle == null)
                {
                    _foldoutStyle = new GUIStyle("ShurikenModuleTitle");
                    _foldoutStyle.font = new GUIStyle(EditorStyles.label).font;
                    _foldoutStyle.border = new RectOffset(15, 7, 4, 4);
                    _foldoutStyle.fixedHeight = 24;
                    _foldoutStyle.contentOffset = new Vector2(20f, -2f);
                }
                return _foldoutStyle;
            }
        }
        void Awake()
        {
            //Set Icons
            MonoScript.FromMonoBehaviour(target as ParticleImage).SetIcon(Resources.Load<Texture2D>("ComponentIcon"));
            _emitterModuleIcon = EditorGUIUtility.IconContent("AreaLight Gizmo").image;
            _particleModuleIcon = Resources.Load<Texture>("ParticleModule");
            _movementModuleIcon = Resources.Load<Texture>("MovementModule"); 
            _eventModuleIcon =  Resources.Load<Texture>("EventModule");
            _advancedModuleIcon = Resources.Load<Texture>("AdvancedIcon");
        }

        private void OnEnable() 
        {
            serializedObject.Update();
            _particle = target as ParticleImage;
            
            //Initialize Properties
            _startPoint = serializedObject.FindProperty("_emitterConstraintEnabled");
            _startPointTrans = serializedObject.FindProperty("_emitterConstraintTransform");
            _timeScale = serializedObject.FindProperty("_timeScale");
            _playMode = serializedObject.FindProperty("_playMode");
            _delay = serializedObject.FindProperty("_startDelay");
            _loop = serializedObject.FindProperty("_loop");
            _prewarm = serializedObject.FindProperty("_prewarm");
            _duration = serializedObject.FindProperty("_duration");
            _maskable = serializedObject.FindProperty("m_Maskable");
            _raycast = serializedObject.FindProperty("m_RaycastTarget");
            _rate = serializedObject.FindProperty("_rate");
            _rateOverLifetime = serializedObject.FindProperty("_rateOverLifetime");
            _rateOverDistance = serializedObject.FindProperty("_rateOverDistance");
            _emitterShape = serializedObject.FindProperty("_shape");
            _spreadType = serializedObject.FindProperty("_spread");
            _spreadLoop = serializedObject.FindProperty("_spreadLoop");
            _space = serializedObject.FindProperty("_space");
            _angle = serializedObject.FindProperty("_angle");
            _width = serializedObject.FindProperty("_width");
            _height = serializedObject.FindProperty("_height");
            _length = serializedObject.FindProperty("_length");
            _radius = serializedObject.FindProperty("_radius");
            _surface = serializedObject.FindProperty("_emitOnSurface");
            _edge = serializedObject.FindProperty("_emitterThickness");
            _colorOverLifetime = serializedObject.FindProperty("_colorOverLifetime");
            _colorBySpeed = serializedObject.FindProperty("_colorBySpeed");
            _colorSpeedRange = serializedObject.FindProperty("_colorSpeedRange");
            _sizeOverLifetime = serializedObject.FindProperty("_sizeOverLifetime");
            _sizeBySpeed = serializedObject.FindProperty("_sizeBySpeed");
            _sizeSpeedRange = serializedObject.FindProperty("_sizeSpeedRange");
            _startRotation = serializedObject.FindProperty("_startRotation");
            _rotateOverLifetime = serializedObject.FindProperty("_rotationOverLifetime");
            _rotationBySpeed = serializedObject.FindProperty("_rotationBySpeed");
            _rotationSpeedRange = serializedObject.FindProperty("_rotationSpeedRange");
            _targetTransform = serializedObject.FindProperty("_attractorTarget");
            _targetCurve = serializedObject.FindProperty("_toTarget");
            _burst = serializedObject.FindProperty("_bursts");
            _speed = serializedObject.FindProperty("_startSpeed");
            _startSize = serializedObject.FindProperty("_startSize");
            _material = serializedObject.FindProperty("m_Material");
            _startColor = serializedObject.FindProperty("_startColor");
            _life = serializedObject.FindProperty("_lifetime");
            _speedOverLifetime = serializedObject.FindProperty("_speedOverLifetime");
            _sprite = serializedObject.FindProperty("m_Sprite");
            _texture = serializedObject.FindProperty("m_Texture");
            _sheetModule = serializedObject.FindProperty("_sheetModule");
            _sheetType = serializedObject.FindProperty("_sheetType");
            _tile = serializedObject.FindProperty("_textureTile");
            _frameOverTime = serializedObject.FindProperty("_frameOverTime");
            _startFrame = serializedObject.FindProperty("_startFrame");
            _frameSpeedRange = serializedObject.FindProperty("_frameSpeedRange");
            _frameFps = serializedObject.FindProperty("_textureSheetFPS");
            _cycles = serializedObject.FindProperty("_textureSheetCycles");
            _targetMode = serializedObject.FindProperty("_targetMode");
            _gravity = serializedObject.FindProperty("_gravity");
            _targetModule = serializedObject.FindProperty("_targetModule");
            _noiseModule = serializedObject.FindProperty("_noiseModule");
            _noiseFreq = serializedObject.FindProperty("_noiseFrequency");
            _noiseOffset = serializedObject.FindProperty("_noiseOffset");
            _noiseStrength = serializedObject.FindProperty("_noiseStrength");
            _gravityModule = serializedObject.FindProperty("_gravityModule");
            _vortexModule = serializedObject.FindProperty("_vortexModule");
            _vortexStrength = serializedObject.FindProperty("_vortexStrength");
            _velocityModule = serializedObject.FindProperty("_velocityModule");
            _velocitySpace = serializedObject.FindProperty("_velocitySpace");
            _velocityOverLifetime = serializedObject.FindProperty("_velocityOverLifetime");
            _alignDirection = serializedObject.FindProperty("_alignToDirection");
            _trailModule = serializedObject.FindProperty("_trailModule");
            _trailWidth = serializedObject.FindProperty("_trailWidth");
            _trailColorOverLifetime = serializedObject.FindProperty("_trailColorOverLifetime");
            _trailColorOverTrail = serializedObject.FindProperty("_trailColorOverTrail");
            _trailMaterial = serializedObject.FindProperty("_trailMaterial");
            _trailLifetime = serializedObject.FindProperty("_trailLifetime");
            _inheritColor = serializedObject.FindProperty("_inheritParticleColor");
            _dieWithParticle = serializedObject.FindProperty("_dieWithParticle");
            _trailRatio = serializedObject.FindProperty("_trailRatio");
            _minimumVertexDistance = serializedObject.FindProperty("_minimumVertexDistance");
            _onStart = serializedObject.FindProperty("_onStart");
            _onFinish = serializedObject.FindProperty("_onStop");
            _onFirstParticleFinish = serializedObject.FindProperty("_onFirstParticleFinish");
            _onLastParticleFinish = serializedObject.FindProperty("_onLastParticleFinish");
            _onParticleFinish = serializedObject.FindProperty("_onParticleFinish");
            _multithreadModule = serializedObject.FindProperty("_multithreadModule");
            _multithreadEnabled = serializedObject.FindProperty("_multithreadEnabled");

            if (Application.isEditor && !EditorApplication.isPlaying)
            {
                _particle.Invoke(nameof(ParticleImage.Play), 0.1f);
                EditorApplication.update += EditorUpdate;
            }
            
            SceneView.duringSceneGui += DrawSceneWindow;
            Undo.undoRedoPerformed += UndoRedoPerformed;
        }

        private void OnDisable()
        {
            EditorApplication.update -= EditorUpdate;
            SceneView.duringSceneGui -= DrawSceneWindow;
            Undo.undoRedoPerformed -= UndoRedoPerformed;
            
            if (Application.isEditor && !EditorApplication.isPlaying && _particle)
            {
                _particle.CancelInvoke();
                _particle.Pause();
            }
        }
        
        private void UndoRedoPerformed()
        {
            // Temporary fix for NullReferenceException on undo performed when Curve Editor is open (Unity bug)
            if (EditorWindow.focusedWindow.titleContent.text == "Curve Editor")
            {
                EditorWindow.focusedWindow.Close();
            }
        }

        void OnSceneGUI()
        {
            Handles.color = Color.cyan;
            Handles.matrix = _particle.transform.localToWorldMatrix;

            // Draw emitter shape
            switch (_particle.shape)
            {
                case EmitterShape.Circle:
                    // Draw inner circle
                    if (!_particle.emitOnSurface)
                    {
                        Handles.color = Color.gray;
                    
                        EditorGUI.BeginChangeCheck();
                        var innerRadiusHandle = Handles.Slider(new Vector3(_particle.circleRadius - _particle.emitterThickness,0f,0f), Vector3.right, HandleUtility.GetHandleSize(new Vector3(_particle.circleRadius - _particle.emitterThickness,0f,0f))  * 0.04f, Handles.DotHandleCap, 0f);
                    
                        if (EditorGUI.EndChangeCheck())
                        {
                            Undo.RecordObject(_particle, "Change Emitter Radius");
                            _particle.emitterThickness = _particle.circleRadius - innerRadiusHandle.x;
                            if(innerRadiusHandle.x > _particle.circleRadius)
                                if(!_particle.fitRect)
                                    _particle.circleRadius = innerRadiusHandle.x;
                                else
                                    _particle.emitterThickness = 0f;
                        }
                    
                        Handles.DrawWireDisc(Vector3.zero, Vector3.forward,
                            _particle.circleRadius - _particle.emitterThickness);
                        
                        Handles.color = Color.cyan;
                    }
                    
                    // Draw outer circle
                    if (!_particle.fitRect)
                    {
                        EditorGUI.BeginChangeCheck();
                        var radiusHandle = Handles.Slider(new Vector3(_particle.circleRadius,0f,0f), Vector3.right, HandleUtility.GetHandleSize(new Vector3(_particle.circleRadius,0f,0f))  * 0.04f, Handles.DotHandleCap, 0f);
                    
                        if (EditorGUI.EndChangeCheck())
                        {
                            Undo.RecordObject(_particle, "Change Emitter Radius");
                            _particle.circleRadius = radiusHandle.x;
                        }
                    }
                    
                    Handles.DrawWireDisc(Vector3.zero, Vector3.forward, _particle.circleRadius);
                    
                    break;
                case EmitterShape.Rectangle:
                    // Draw inner rectangle
                    if(!_particle.emitOnSurface)
                    {
                        Handles.color = Color.gray;
                    
                        EditorGUI.BeginChangeCheck();
                        var innerRectWidthHandle = Handles.Slider(new Vector3((_particle.rectWidth - _particle.emitterThickness) / 2,0f,0f), Vector3.right, HandleUtility.GetHandleSize(new Vector3((_particle.rectWidth - _particle.emitterThickness) / 2,0f,0f))  * 0.04f, Handles.DotHandleCap, 0f);
                        if (EditorGUI.EndChangeCheck())
                        {
                            Undo.RecordObject(_particle, "Change Emitter Width");
                            _particle.emitterThickness = _particle.rectWidth - innerRectWidthHandle.x * 2;
                            if(innerRectWidthHandle.x > _particle.rectWidth / 2)
                                if(!_particle.fitRect)
                                    _particle.rectWidth = innerRectWidthHandle.x * 2;
                                else
                                    _particle.emitterThickness = 0f;
                        }
                    
                        Handles.DrawWireCube(Vector3.zero, new Vector3(_particle.rectWidth - _particle.emitterThickness, _particle.rectHeight - _particle.emitterThickness));
                        
                        Handles.color = Color.cyan;
                    }
                    
                    // Draw outer rectangle
                    if (!_particle.fitRect)
                    {
                        EditorGUI.BeginChangeCheck();
                        var rectWidthHandle = Handles.Slider(new Vector3(_particle.rectWidth / 2, 0f, 0f),
                            Vector3.right,
                            HandleUtility.GetHandleSize(new Vector3(_particle.rectWidth / 2, 0f, 0f)) * 0.04f,
                            Handles.DotHandleCap, 0f);
                        if (EditorGUI.EndChangeCheck())
                        {
                            Undo.RecordObject(_particle, "Change Emitter Width");
                            _particle.rectWidth = rectWidthHandle.x * 2;
                        }

                        EditorGUI.BeginChangeCheck();
                        var rectHeightHandle = Handles.Slider(new Vector3(0f, _particle.rectHeight / 2, 0f), Vector3.up,
                            HandleUtility.GetHandleSize(new Vector3(0f, _particle.rectHeight / 2, 0f)) * 0.04f,
                            Handles.DotHandleCap, 0f);
                        if (EditorGUI.EndChangeCheck())
                        {
                            Undo.RecordObject(_particle, "Change Emitter Height");
                            _particle.rectHeight = rectHeightHandle.y * 2;
                        }
                    }
                    
                    Handles.DrawWireCube(Vector3.zero, new Vector3(_particle.rectWidth, _particle.rectHeight));
                    
                    break;
                case EmitterShape.Line:
                    if (!_particle.fitRect)
                    {
                        EditorGUI.BeginChangeCheck();
                        var lineLengthHandle = Handles.Slider(new Vector3(_particle.lineLength / 2,0f,0f), Vector3.right, HandleUtility.GetHandleSize(new Vector3(_particle.lineLength / 2,0f,0f))  * 0.04f, Handles.DotHandleCap, 0f);
                        if (EditorGUI.EndChangeCheck())
                        {
                            Undo.RecordObject(_particle, "Change Emitter Length");
                            _particle.lineLength = lineLengthHandle.x * 2;
                        }
                    }

                    Handles.DrawLine(new Vector3(-_particle.lineLength/2, 0, 0), new Vector3(_particle.lineLength/2, 0, 0));
                    break;
                
                case EmitterShape.Directional:
                    EditorGUI.BeginChangeCheck();
                    var angleHandle = Handles.Slider(PointInCircle(_angle.floatValue/2).normalized * 100f, Vector3.right, HandleUtility.GetHandleSize(PointInCircle(_angle.floatValue/2).normalized * 100f) * 0.04f, Handles.DotHandleCap, 0f);
                    if (EditorGUI.EndChangeCheck())
                    {
                        Undo.RecordObject(_particle, "Change Emitter Angle");
                        _particle.directionAngle = Vector3.SignedAngle(Vector3.right, angleHandle, Vector3.forward);
                    }
                    
                    Handles.DrawWireArc(Vector3.zero, Vector3.forward, PointInCircle(_angle.floatValue/2).normalized, _angle.floatValue,100);
                    Handles.DrawLine(Vector3.zero, PointInCircle(_angle.floatValue/2).normalized * 100f);
                    Handles.DrawLine(Vector3.zero, PointInCircle(-_angle.floatValue/2).normalized * 100f);
                    break;
            }

            Handles.color = Color.white;
        }
        
        // Get point on circle from angle
        private Vector3 PointInCircle(float angle){
            var rad = angle * Mathf.Deg2Rad;
            return new Vector3(Mathf.Sin( rad ), Mathf.Cos( rad ), 0);
        }

        // Draw control panel in scene view
        void DrawSceneWindow(SceneView sceneView)
        {
            Handles.BeginGUI();
            
            Rect rect;

            if (PrefabStageUtility.GetCurrentPrefabStage())
            {
                rect = new Rect(Screen.width / EditorGUIUtility.pixelsPerPoint - 230,
                    Screen.height / EditorGUIUtility.pixelsPerPoint - 168, 220, 88);
            }
            else
            {
                rect = new Rect(Screen.width / EditorGUIUtility.pixelsPerPoint - 230,
                    Screen.height / EditorGUIUtility.pixelsPerPoint - 142, 220, 88);
            }

            GUILayout.BeginArea(rect, new GUIContent(_particle.gameObject.name),new GUIStyle("window"));
            
            GUILayout.BeginHorizontal();

            if (GUILayout.Button(_particle.isPlaying ? "Pause" : "Play", new GUIStyle("ButtonLeft")))
            {
                if (_particle.isPlaying)
                    _particle.Pause();
                else
                    _particle.Play();
            }

            if (GUILayout.Button("Reset", new GUIStyle("ButtonMid")))
            {
                _particle.Stop(true);
                _particle.Play();
            }

            if (GUILayout.Button("Stop", new GUIStyle("ButtonRight")))
            {
                _particle.Stop(true);
            }

            GUILayout.EndHorizontal();
            EditorGUILayout.LabelField("Playback Time",_particle.main ? _particle.main.playback.ToString("F") : _particle.playback.ToString("F"));
            if (_particle.multithreadEnabled)
            {
#if PARTICLE_IMAGE_JOBS
                EditorGUILayout.LabelField("Particles",_particle.JobParticles.Length.ToString());
#endif
            }
            else
            {
                EditorGUILayout.LabelField("Particles",_particle.particles.Count.ToString());
            }

            GUILayout.EndArea();
            
            Handles.EndGUI();
        }

        private bool showWarning;
        
        public override void OnInspectorGUI()
        {
            EditorGUILayout.PropertyField(_space, new GUIContent("Simulation Space"), false);
            EditorGUI.BeginChangeCheck();
            EditorGUILayout.PropertyField(_timeScale, new GUIContent("Simulation Time"), false);
            if (EditorGUI.EndChangeCheck())
            {
                _particle.Stop(true);
            }
            EditorGUI.BeginChangeCheck();
            EditorGUILayout.PropertyField(_playMode, new GUIContent("Play*", "Shared between the particles group"), false);
            if (EditorGUI.EndChangeCheck())
            {
                _particle.PlayMode = (PlayMode)_playMode.enumValueIndex;
                EditorUtility.SetDirty(_particle);
            }
            EditorGUI.BeginChangeCheck();
            EditorGUILayout.PropertyField(_loop, false);
            if (EditorGUI.EndChangeCheck())
            {
                _prewarm.boolValue = false;
            }
            EditorGUI.BeginDisabledGroup(!_particle.loop);
            if (_prewarm.boolValue)
            {
                GUILayout.BeginHorizontal();
            }
            
            EditorGUILayout.PropertyField(_prewarm, false);
            
            if (_prewarm.boolValue)
            {
                if (GUILayout.Button(new GUIContent(EditorGUIUtility.IconContent("d_console.warnicon.sml").image, "Prewarm does not support a large number of particles."), GUIStyle.none,
                        GUILayout.Width(22), GUILayout.Height(22)))
                {
                    showWarning = !showWarning;
                }
                GUILayout.EndHorizontal();
            }
            if (showWarning)
            {
                EditorGUILayout.HelpBox("Prewarm does not support a large number of particles.", MessageType.Warning);
            }
            
            EditorGUI.EndDisabledGroup();
            EditorGUI.BeginDisabledGroup(_particle.prewarm);
            EditorGUILayout.PropertyField(_delay,new GUIContent("Start Delay"), false);
            EditorGUI.EndDisabledGroup();
            EditorGUILayout.PropertyField(_duration, false);
            EditorGUILayout.PropertyField(_life, false);
            EditorGUILayout.PropertyField(_speed, false);
            EditorGUILayout.PropertyField(_startSize, new GUIContent("Start Size"), false);
            EditorGUILayout.PropertyField(_startRotation, new GUIContent("Start Rotation"), false);
            EditorGUILayout.PropertyField(_startColor, new GUIContent("Start Color"), false);
            EditorGUILayout.PropertyField(_raycast, false);
            EditorGUILayout.PropertyField(_maskable, false);
            
            _particle.moduleEmitterFoldout = Foldout("Emission", _particle.moduleEmitterFoldout, _emitterModuleIcon);
            if (_particle.moduleEmitterFoldout)
            {
                EditorGUILayout.PropertyField(_rate, new GUIContent("Rate per Second"), false);
                EditorGUILayout.PropertyField(_rateOverLifetime, new GUIContent("Rate over Duration") ,false);
                EditorGUILayout.PropertyField(_rateOverDistance, new GUIContent("Rate over Distance"), false);
                EditorGUILayout.PropertyField(_burst, true);
                DrawHorizontalLine();
                EditorGUILayout.PropertyField(_emitterShape, new GUIContent("Shape"),false);
                switch (_particle.shape)
                {
                    case EmitterShape.Point:
                        EditorGUILayout.PropertyField(_spreadType, new GUIContent("Spread"), false);
                        if (_particle.spreadType == SpreadType.Uniform)
                        {
                            EditorGUILayout.PropertyField(_spreadLoop, new GUIContent("Loop"), false);
                        }
                        break;
                    case EmitterShape.Circle:
                        EditorGUI.BeginDisabledGroup(_particle.fitRect);
                        EditorGUILayout.PropertyField(_radius,new GUIContent("Radius"), false);
                        EditorGUI.EndDisabledGroup();
                        _particle.fitRect = EditorGUILayout.Toggle("Fit Rect", _particle.fitRect);
                        EditorGUILayout.PropertyField(_surface, false);
                        if(!_surface.boolValue)
                            EditorGUILayout.PropertyField(_edge, false);
                        
                        _edge.floatValue= Mathf.Clamp(_edge.floatValue, 0f, _particle.circleRadius);

                        EditorGUILayout.PropertyField(_spreadType, new GUIContent("Spread"), false);
                        if (_particle.spreadType == SpreadType.Uniform)
                        {
                            EditorGUILayout.PropertyField(_spreadLoop, new GUIContent("Loop"), false);
                        }
                        break;
                    case EmitterShape.Rectangle:
                        EditorGUI.BeginDisabledGroup(_particle.fitRect);
                        EditorGUILayout.PropertyField(_width, false);
                        EditorGUILayout.PropertyField(_height, false);
                        EditorGUI.EndDisabledGroup();
                        _particle.fitRect = EditorGUILayout.Toggle("Fit Rect",_particle.fitRect);
                        EditorGUILayout.PropertyField(_surface, false);
                        if(!_surface.boolValue)
                            EditorGUILayout.PropertyField(_edge, false);
                        _edge.floatValue= Mathf.Clamp(_edge.floatValue, 0f, _particle.rectTransform.sizeDelta.x<_particle.rectTransform.sizeDelta.y?_particle.rectWidth:_particle.rectHeight);
                        EditorGUILayout.PropertyField(_spreadType, new GUIContent("Spread"), false);
                        if (_particle.spreadType == SpreadType.Uniform)
                        {
                            EditorGUILayout.PropertyField(_spreadLoop, new GUIContent("Loop"), false);
                        }
                        break;
                    case EmitterShape.Line:
                        EditorGUI.BeginDisabledGroup(_particle.fitRect);
                        EditorGUILayout.PropertyField(_length, false);
                        EditorGUI.EndDisabledGroup();
                        _particle.fitRect = EditorGUILayout.Toggle("Fit Rect",_particle.fitRect);
                        EditorGUILayout.PropertyField(_spreadType, new GUIContent("Spread"), false);
                        if (_particle.spreadType == SpreadType.Uniform)
                        {
                            EditorGUILayout.PropertyField(_spreadLoop, new GUIContent("Loop"), false);
                        }
                        break;
                    case EmitterShape.Directional:
                        EditorGUILayout.PropertyField(_angle, false);
                        EditorGUILayout.PropertyField(_spreadType, new GUIContent("Spread"), false);
                        if (_particle.spreadType == SpreadType.Uniform)
                        {
                            EditorGUILayout.PropertyField(_spreadLoop, new GUIContent("Loop"), false);
                        }
                        break;
                }
                
                DrawHorizontalLine();

                EditorGUILayout.PropertyField(_startPoint,new GUIContent("Emitter Constraint"), false);
                if (_particle.emitterConstraintEnabled)
                {
                    EditorGUILayout.PropertyField(_startPointTrans,new GUIContent("Transform"), false);
                }
                
                GUILayout.Space(2);
            }

            _particle.moduleParticleFoldout = Foldout("Particle", _particle.moduleParticleFoldout,_particleModuleIcon);
            if (_particle.moduleParticleFoldout)
            {
                EditorGUILayout.PropertyField(_sprite, new GUIContent("Sprite"));
                EditorGUILayout.PropertyField(_material, new GUIContent("Material"));
                GUI.color = Color.gray;
                EditorGUILayout.PropertyField(_texture, new GUIContent("Texture (Deprecated)*", "Use Sprite instead"));
                GUI.color = Color.white;
                
                DrawHorizontalLine();
                
                EditorGUILayout.PropertyField(_speedOverLifetime, false);
                
                DrawHorizontalLine();
                
                EditorGUILayout.PropertyField(_sizeOverLifetime, false);
                EditorGUILayout.PropertyField(_sizeBySpeed, false);
                EditorGUILayout.PropertyField(_sizeSpeedRange, new GUIContent("Size Speed Range"), false);
                
                DrawHorizontalLine();
                
                EditorGUILayout.PropertyField(_colorOverLifetime, false);
                EditorGUILayout.PropertyField(_colorBySpeed, false);
                EditorGUILayout.PropertyField(_colorSpeedRange, new GUIContent("Color Speed Range"), false);
                
                DrawHorizontalLine();
                
                EditorGUILayout.PropertyField(_rotateOverLifetime, false);
                EditorGUILayout.PropertyField(_rotationBySpeed, false);
                EditorGUILayout.PropertyField(_rotationSpeedRange, new GUIContent("Rotation Speed Range"), false);
                EditorGUILayout.PropertyField(_alignDirection);

                DrawHorizontalLine();

                EditorGUILayout.PropertyField(_sheetModule, new GUIContent("Texture Sheet"));
                
                if (_sheetModule.FindPropertyRelative("enabled").boolValue)
                {
                    EditorGUILayout.PropertyField(_tile, new GUIContent("Tiles"), false);
                    EditorGUILayout.PropertyField(_sheetType, new GUIContent("Time Mode"), false);
                    
                    switch (_particle.textureSheetType)
                    {
                        case SheetType.Lifetime:
                            EditorGUILayout.PropertyField(_frameOverTime, false);
                            EditorGUILayout.PropertyField(_startFrame, false);
                            EditorGUILayout.PropertyField(_cycles, false);
                            ParticleSystem.MinMaxCurve c = _particle.textureSheetFrameOverTime;
                            switch (_particle.textureSheetFrameOverTime.mode)
                            {
                                case ParticleSystemCurveMode.Curve:
                                    c.curveMultiplier = Mathf.Clamp(_particle.textureSheetFrameOverTime.curveMultiplier, 0, _particle.textureTile.x*_particle.textureTile.y);
                                    break;
                                case ParticleSystemCurveMode.TwoCurves:
                                    c.curveMultiplier = Mathf.Clamp(_particle.textureSheetFrameOverTime.curveMultiplier, 0, _particle.textureTile.x*_particle.textureTile.y);
                                    break;
                                case ParticleSystemCurveMode.TwoConstants:
                                    c.constantMax = Mathf.Clamp(_particle.textureSheetFrameOverTime.constantMax, 0, _particle.textureTile.x*_particle.textureTile.y);
                                    break;
                                case ParticleSystemCurveMode.Constant:
                                    c.constant = Mathf.Clamp(_particle.textureSheetFrameOverTime.constant, 0, _particle.textureTile.x*_particle.textureTile.y);
                                    break;
                            }
                            _particle.textureSheetFrameOverTime = c;
                            break;
                        case SheetType.Speed:
                            EditorGUILayout.PropertyField(_frameSpeedRange, new GUIContent("Speed Range"), false);
                            EditorGUILayout.PropertyField(_startFrame, false);
                            break;
                        case SheetType.FPS:
                            EditorGUILayout.PropertyField(_startFrame, false);
                            EditorGUILayout.PropertyField(_frameFps, new GUIContent("FPS"),false);
                            break;
                    }

                    ParticleSystem.MinMaxCurve sf = _particle.textureSheetStartFrame;
                    switch (_particle.textureSheetStartFrame.mode)
                    {
                        case ParticleSystemCurveMode.Curve:
                            sf.curveMultiplier =  Mathf.Clamp(_particle.textureSheetStartFrame.curveMultiplier, 0, _particle.textureTile.x*_particle.textureTile.y);
                            break;
                        case ParticleSystemCurveMode.TwoCurves:
                            sf.curveMultiplier = Mathf.Clamp(_particle.textureSheetStartFrame.curveMultiplier, 0, _particle.textureTile.x*_particle.textureTile.y);
                            break;
                        case ParticleSystemCurveMode.TwoConstants:
                            sf.constantMax = Mathf.Clamp(_particle.textureSheetStartFrame.constantMax, 0, _particle.textureTile.x*_particle.textureTile.y);
                            break;
                        case ParticleSystemCurveMode.Constant:
                            sf.constant = Mathf.Clamp(_particle.textureSheetStartFrame.constant, 0, _particle.textureTile.x*_particle.textureTile.y);
                            break;
                    }

                    _particle.textureSheetStartFrame = sf;
                }
                
                DrawHorizontalLine();
                
                EditorGUI.BeginChangeCheck();
                EditorGUILayout.PropertyField(_trailModule, new GUIContent("Trails"), false);
                if (EditorGUI.EndChangeCheck())
                {
                    if (_trailModule.FindPropertyRelative("enabled").boolValue)
                    {
                        if (_particle.particleTrailRenderer == null)
                        {
                            GameObject tr = new GameObject("Trails");
                            tr.transform.parent = _particle.transform;
                            tr.transform.localPosition = Vector3.zero;
                            tr.transform.localScale = Vector3.one;
                            tr.transform.localEulerAngles = Vector3.zero;
                            tr.AddComponent<CanvasRenderer>();
                            ParticleTrailRenderer r = tr.AddComponent<ParticleTrailRenderer>();
                            r.raycastTarget = false;
                            _particle.particleTrailRenderer = r;
                            r.particle = _particle;
                            
                            if (_particle.isPlaying)
                            {
                                _particle.Stop(true);
                                _particle.Play();
                            }
                        }
                    }
                    else
                    {
                        var ch = _particle.GetComponentsInChildren<ParticleTrailRenderer>();
                        for (int i = 0; i < ch.Length; i++)
                        {
                            DestroyImmediate(ch[i].gameObject);
                        }
                    }
                }
                if (_trailModule.FindPropertyRelative("enabled").boolValue)
                {
                    EditorGUILayout.PropertyField(_trailRatio, new GUIContent("Trail Ratio"),false);
                    EditorGUILayout.PropertyField(_minimumVertexDistance, new GUIContent("Vertex Distance"),false);
                    EditorGUILayout.PropertyField(_trailLifetime, new GUIContent("Trail Lifetime"),false);
                    EditorGUILayout.PropertyField(_trailWidth, new GUIContent("Trail Width"),false);
                    EditorGUILayout.PropertyField(_inheritColor, new GUIContent("Inherit Particle Color"),false);
                    EditorGUILayout.PropertyField(_trailColorOverLifetime, new GUIContent("Color over Lifetime"),false);
                    EditorGUILayout.PropertyField(_trailColorOverTrail, new GUIContent("Color over Trail"),false);
                    EditorGUI.BeginChangeCheck();
                    EditorGUILayout.PropertyField(_trailMaterial, new GUIContent("Trail Material"),false);
                    if (EditorGUI.EndChangeCheck())
                    {
                        if (_particle.particleTrailRenderer != null)
                        {
                            _particle.particleTrailRenderer.material = _trailMaterial.objectReferenceValue as Material;
                            _particle.particleTrailRenderer.SetMaterialDirty();
                        }
                    }
                    EditorGUILayout.PropertyField(_dieWithParticle, new GUIContent("Die With Particle"),false);
                }
                GUILayout.Space(4);
            }

            _particle.moduleMovementFoldout = Foldout("Movement", _particle.moduleMovementFoldout,_movementModuleIcon);
            if (_particle.moduleMovementFoldout)
            {
                EditorGUILayout.PropertyField(_targetModule, new GUIContent("Attractor"),false);
                if (_targetModule.FindPropertyRelative("enabled").boolValue)
                {
                    EditorGUILayout.PropertyField(_targetTransform,new GUIContent("Attractor"), false);
                    EditorGUILayout.PropertyField(_targetCurve,new GUIContent("Attractor Lerp"), false);
                    if(_particle.attractorTarget != null)
                        EditorGUI.BeginDisabledGroup(_particle.attractorTarget.GetType() != typeof(RectTransform));
                    else
                        EditorGUI.BeginDisabledGroup(false);
                    EditorGUILayout.PropertyField(_targetMode, new GUIContent("Attractor Mode"),false);
                    EditorGUI.EndDisabledGroup();
                }
                DrawHorizontalLine();
                EditorGUILayout.PropertyField(_noiseModule, new GUIContent("Noise"), false);
                if (_noiseModule.FindPropertyRelative("enabled").boolValue)
                {
                    EditorGUILayout.PropertyField(_noiseStrength,new GUIContent("Strength"), false);
                    
                    EditorGUI.BeginChangeCheck();
                    EditorGUILayout.PropertyField(_noiseFreq,new GUIContent("Frequency"), false);
                    if (EditorGUI.EndChangeCheck())
                    {
                        _particle.noise.SetFrequency(_particle.noiseFrequency/100f);
                    }
                    
                    EditorGUILayout.PropertyField(_noiseOffset, new GUIContent("Offset"),false);
                    
                    _particle.noiseDebug = EditorGUILayout.Toggle("Visualize Noise",_particle.noiseDebug);

                    if (_particle.noiseDebug)
                    {
                        _particle.noiseViewSize = EditorGUILayout.Vector2IntField("Size",_particle.noiseViewSize);
                    }
                    
                    
                }

                DrawHorizontalLine();
                EditorGUILayout.PropertyField(_velocityModule, new GUIContent("Velocity"), false);
                if (_velocityModule.FindPropertyRelative("enabled").boolValue)
                {
                    EditorGUILayout.PropertyField(_velocitySpace,new GUIContent("Space"), false);
                    EditorGUILayout.PropertyField(_velocityOverLifetime,new GUIContent("Velocity Over Lifetime"), false);
                }
                DrawHorizontalLine();
                EditorGUILayout.PropertyField(_gravityModule, new GUIContent("Gravity"), false);
                if (_gravityModule.FindPropertyRelative("enabled").boolValue)
                {
                    EditorGUILayout.PropertyField(_gravity,new GUIContent("Gravity Force"), false);
                }
                DrawHorizontalLine();
                EditorGUILayout.PropertyField(_vortexModule, new GUIContent("Vortex"), false);
                if (_vortexModule.FindPropertyRelative("enabled").boolValue)
                {
                    EditorGUILayout.PropertyField(_vortexStrength, false);
                }
                GUILayout.Space(4);
            }
            _particle.moduleEventsFoldout = Foldout("Events", _particle.moduleEventsFoldout,_eventModuleIcon);
            if (_particle.moduleEventsFoldout)
            {
                EditorGUILayout.PropertyField(_onStart, new GUIContent("On Particle Start"), false);
                EditorGUILayout.PropertyField(_onFirstParticleFinish, new GUIContent("On First Particle Finished"),false);
                EditorGUILayout.PropertyField(_onParticleFinish, new GUIContent("On Any Particle Finished"),false);
                EditorGUILayout.PropertyField(_onLastParticleFinish, new GUIContent("On Last Particle Finished"),false);
                EditorGUILayout.PropertyField(_onFinish, new GUIContent("On Particle Stopped"),false);
            }
            
            _particle.moduleAdvancedFoldout = Foldout("Advanced", _particle.moduleAdvancedFoldout,_advancedModuleIcon);
            if (_particle.moduleAdvancedFoldout)
            {
                EditorGUI.BeginChangeCheck();
                EditorGUILayout.PropertyField(_multithreadModule, new GUIContent("Multithreading*", "Shared between the particles group"), false);
                if (EditorGUI.EndChangeCheck())
                {
                    if (_particle.isPlaying)
                    {
                        _particle.Stop(true);
                        _particle.Play();
                    }
                    if(_multithreadModule.FindPropertyRelative("enabled").boolValue == false)
                        _particle.multithreadEnabled = false;
                }
                
                if (_multithreadModule.FindPropertyRelative("enabled").boolValue)
                {
                    var labelStyle = new GUIStyle(GUI.skin.label) {richText = true};
                    
                    EditorGUI.BeginDisabledGroup(!hasBurst || !hasCollections || !hasMathematics);
                    EditorGUI.BeginChangeCheck();
                    EditorGUILayout.PropertyField(_multithreadEnabled, new GUIContent("Enable", "Multithreaded can't be enabled until all package dependencies are installed"), false);
                    if (EditorGUI.EndChangeCheck())
                    {
                        if (_particle.isPlaying)
                        {
                            _particle.Stop(true);
                            _particle.Play();
                        }
                        _particle.multithreadEnabled = _multithreadEnabled.boolValue;
                    }
                    EditorGUI.EndDisabledGroup();
                    
    #if UNITY_COLLECTIONS
                    hasCollections = true;
                    GUILayout.BeginHorizontal(EditorStyles.helpBox);
                    GUILayout.Label(new GUIContent("<b>com.unity.collections</b> has been found", EditorGUIUtility.IconContent("Installed").image), labelStyle);
                    GUILayout.EndHorizontal();
    #else
                    hasCollections = false;
                    GUILayout.BeginHorizontal(EditorStyles.helpBox);
                    EditorGUILayout.LabelField(new GUIContent("<b>com.unity.collections</b> needs to be installed", EditorGUIUtility.IconContent("Error").image), labelStyle);
                    if(GUILayout.Button("Install", GUILayout.Width(80)))
                    {
                        UnityEditor.PackageManager.UI.Window.Open("com.unity.collections");
                    }
                    GUILayout.EndHorizontal();
    #endif
                    
    #if UNITY_MATHEMATICS
                    hasMathematics = true;
                    GUILayout.BeginHorizontal(EditorStyles.helpBox);
                    GUILayout.Label(new GUIContent("<b>com.unity.mathematics</b> has been found", EditorGUIUtility.IconContent("Installed").image), labelStyle);
                    GUILayout.EndHorizontal();
    #else
                    hasMathematics = false;
                    GUILayout.BeginHorizontal(EditorStyles.helpBox);
                    EditorGUILayout.LabelField(new GUIContent("<b>com.unity.mathematics</b> needs to be installed", EditorGUIUtility.IconContent("Error").image), labelStyle);
                    if(GUILayout.Button("Install", GUILayout.Width(80)))
                    {
                        UnityEditor.PackageManager.UI.Window.Open("com.unity.mathematics");
                    }
                    GUILayout.EndHorizontal();
    #endif
                    
    #if UNITY_BURST
                    hasBurst = true;
                    GUILayout.BeginHorizontal(EditorStyles.helpBox);
                    GUILayout.Label(new GUIContent("<b>com.unity.burst</b> has been found", EditorGUIUtility.IconContent("Installed").image), labelStyle);
                    GUILayout.EndHorizontal();
    #else
                    hasBurst = false;
                    GUILayout.BeginHorizontal(EditorStyles.helpBox);
                    GUILayout.Label(new GUIContent("<b>com.unity.burst</b> needs to be installed", EditorGUIUtility.IconContent("Error").image), labelStyle);
                    if(GUILayout.Button("Install", GUILayout.Width(80)))
                    {
                        UnityEditor.PackageManager.UI.Window.Open("com.unity.burst");
                    }
                    GUILayout.EndHorizontal();
    #endif
                    
                    _multithreadEnabled.boolValue = _multithreadEnabled.boolValue && hasBurst && hasCollections && hasMathematics;
                }
            }
            serializedObject.ApplyModifiedProperties();
        }
        
        void EditorUpdate()
        {
            EditorApplication.QueuePlayerLoopUpdate();
        } 
        
        private void DrawHorizontalLine(int height = 1) {
            GUILayout.Space(2);
 
            Rect rect = GUILayoutUtility.GetRect(10, height, GUILayout.ExpandWidth(true));
            rect.height = height;
            rect.xMin = 18;
            rect.xMax = EditorGUIUtility.currentViewWidth - 18;
 
            Color lineColor = new Color(0.15f, 0.15f, 0.15f, 1);
            EditorGUI.DrawRect(rect, lineColor);
            GUILayout.Space(2);
        }

        private bool Foldout(string title, bool display, Texture icon)
        {
            var rect = GUILayoutUtility.GetRect(16f, 24f, FoldoutStyle);
            GUI.Box(rect, title, FoldoutStyle);

            var e = Event.current;

            var toggleRect = new Rect(rect.x + 4f, rect.y + 4f, 13f, 13f);
            if (e.type == EventType.Repaint)
            {
                EditorStyles.foldout.Draw(new Rect(rect.width - 4f, rect.y + 4f, 13f, 13f), false, false, display, false);
                GUI.DrawTexture(toggleRect, icon);
            }

            if (e.type == EventType.MouseDown && rect.Contains(e.mousePosition))
            {
                display = !display;
                e.Use();
            }

            return display;
        }
    }
}