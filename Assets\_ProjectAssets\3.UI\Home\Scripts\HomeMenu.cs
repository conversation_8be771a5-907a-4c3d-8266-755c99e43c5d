using System.Collections;
using DSDK.Data;
using DSDK.UISystem;
using TMPro;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;

public class HomeMenu : UIPanel
{
    [SerializeField] private Animator homeMenuAnimator;
    [SerializeField] private Button homeBtn;
    [SerializeField] private Button shopBtn;
    [SerializeField] private BaseBtn settingBtn;
    [SerializeField] private BaseBtn playBtn;
    [SerializeField] private TextMeshProUGUI levelText;

    void Start()
    {
        homeBtn.onClick.AddListener(OnHomeBtnClick);
        shopBtn.onClick.AddListener(OnShopBtnClick);
        settingBtn.OnClick(OnSettingBtnClick);
        playBtn.OnClick(OnPlayBtnClick);
    }
    public override void Show()
    {
        base.Show();
        UISystem.Instance.ShowPanel<CurrencyOverlay>().SetHeartPosition(true);
        levelText.text = "Level " + GameData.Instance.CurrentLevelIndex;
    }
    private void OnSettingBtnClick()
    {
        UISystem.Instance.ShowPanel<SettingsPopup>().CheckIfIsInMainMenu(true);
    }

    private void OnHomeBtnClick()
    {
        homeMenuAnimator.SetTrigger("Home");
    }

    private void OnShopBtnClick()
    {
        homeMenuAnimator.SetTrigger("Shop");
    }
    private AsyncOperation asyncOperation;
    private void OnPlayBtnClick()
    {
        if (GameData.Instance.CurrentHeart <= 0)
        {
            UISystem.Instance.ShowPanel<RefillHeartPopup>().SetForceLoadScene(false);
            return;
        }

        // GameData.Instance.CurrentHeart--;
        asyncOperation = SceneManager.LoadSceneAsync(2);
        asyncOperation.allowSceneActivation = false;
        StartCoroutine(ShowLoading());
    }
    private IEnumerator ShowLoading()
    {
        UISystem.Instance.ShowPanel<LoadingOverlay>();
        yield return new WaitForSeconds(1.5f);
        // loading.Hide();
        asyncOperation.allowSceneActivation = true;
    }

}
