using UnityEngine;
using DSDK.Extensions;
using DSDK.Core;
using System.Collections;
using System.Linq;
using DG.Tweening;

public class ParkingSlotsManager : MonoBehaviour
{
    private HumanoidFactory humanoidFactory;
    [SerializeField] private ParkingSlot[] parkingSlots;
    [SerializeField] private CornerPoints cornerPoints;
    [SerializeField] private Transform exitPoint;
    public ParkingSlot warningParkingSlot;
    public Transform ExitPoint => exitPoint;
    private bool CheckingFactoryLines = false;
    private Coroutine checkFactoryLinesCoroutine;
    private const float ToastInterval = 10f;
    private float lastToastTime = -10f;

    void OnEnable()
    {
        this.AddEventListener(EventID.CarParked, OnCarParked);
    }
    void OnDisable()
    {
        this.RemoveEventListener(EventID.CarParked, OnCarParked);
        if (delayLoseTween != null)
            delayLoseTween.Kill();
    }


    public void Initialize(HumanoidFactory humanoidFactory)
    {
        this.humanoidFactory = humanoidFactory;
        for (int i = 0; i < parkingSlots.Length; i++)
        {
            parkingSlots[i].Initialize(i >= GameManager.Instance.GameConfigManager.slotConfig.startSlot);
        }
    }

    private void OnCarParked()
    {
        if (CheckingFactoryLines)
        {
            return;
        }

        CheckingFactoryLines = true;
        if (checkFactoryLinesCoroutine != null) return;
        checkFactoryLinesCoroutine = StartCoroutine(CheckFactoryLines());
    }
    public ParkingSlot[] sortedSlots;
    private IEnumerator CheckFactoryLines()
    {
        while (CheckingFactoryLines)
        {
            bool canLose = true;
            if (delayLoseTween != null)
            {
                this.DispatchEvent(EventID.OnWarning, false);
                delayLoseTween.Kill();
            }
            // yield return new WaitUntil(() => GameManager.Instance.CurrentGameState == EGameState.Playing);
            yield return new WaitUntil(() => !humanoidFactory.isShifting);

            sortedSlots = parkingSlots.Where(slot => slot.ParkedVehicle != null).OrderByDescending(slot => slot.ParkedVehicle.sittingHumanoids.Count).ToArray();
            for (int i = 0; i < sortedSlots.Length; i++)
            {
                // if (sortedSlots[i].IsLocked || sortedSlots[i].IsEmpty)
                // {
                //     continue;
                // }
                // else
                // {
                Vehicle vehicle = sortedSlots[i].ParkedVehicle;

                if (vehicle.HasEmptySeat && vehicle.WasParked)
                {
                    HumanoidBlock humanoid = humanoidFactory.GetHumanoid(vehicle.VehicleType, vehicle.Color, out Transform dropDownPoint);
                    if (humanoid != null)
                    {
                        canLose = false;
                        humanoidFactory.AddRunningHumanoid(humanoid);
                        yield return null;
                        humanoid.StartAction(dropDownPoint, sortedSlots[i].ParkedVehicle, () => humanoidFactory.RemoveRunningHumanoid());
                        sortedSlots[i].ParkedVehicle.FillVehicle(humanoid);
                        break;
                    }
                }
                else
                {
                    canLose = false;
                }
                // }
            }
            yield return new WaitForSeconds(0.1f);
            if (parkingSlots.Where(slot => !slot.IsLocked).All(slot => slot.IsEmpty))
            {
                StopCheckFactoryCoroutine();
            }


            if (canLose)
            {

                bool allSlotsHaveVehicle = parkingSlots.Where(slot => !slot.IsLocked).All(slot => !slot.IsEmpty && slot.ParkedVehicle.WasParked);
                bool allVehiclesStillNeedHumanoid = parkingSlots
                    .Where(slot => !slot.IsEmpty)
                    .All(slot => slot.ParkedVehicle.HasEmptySeat);


                if (allSlotsHaveVehicle && allVehiclesStillNeedHumanoid)
                {
                    if (humanoidFactory.RunningHumanoidCount > 0)
                    {
                        yield return new WaitUntil(() => humanoidFactory.RunningHumanoidCount == 0);
                        continue;
                    }
                    if (humanoidFactory.isShifting)
                    {
                        continue;
                    }
                    StopCheckFactoryCoroutine();
                    DelayCallLose();
                }
                else
                {
                    StopCheckFactoryCoroutine();
                }
            }
        }
    }
    Tween delayLoseTween;
    private void DelayCallLose()
    {
        GameManager.Instance.ShowErrorToast("Slot full");
        this.DispatchEvent(EventID.OnWarning, true);
        delayLoseTween = DOVirtual.DelayedCall(4f, () =>
        {
            this.DispatchEvent(EventID.OnWarning, false);
            this.DispatchEvent(EventID.OnLose);
        }).OnUpdate(() =>
        {
            if (GetAvailableSlot() != null)
            {
                this.DispatchEvent(EventID.OnWarning, false);
                delayLoseTween.Kill();
            }
        });
    }
    void OnDestroy()
    {
        if (delayLoseTween != null)
            delayLoseTween.Kill();
    }

    private void StopCheckFactoryCoroutine()
    {
        if (checkFactoryLinesCoroutine != null)
        {
            StopCoroutine(checkFactoryLinesCoroutine);
            checkFactoryLinesCoroutine = null;
            CheckingFactoryLines = false;
        }
    }

    public ParkingSlot GetAvailableSlot()
    {
        ParkingSlot slot = parkingSlots.FirstOrDefault(slot => !slot.IsLocked && slot.IsEmpty);
        return slot;
    }

    public static Vector3[] GeneratePathForHumanoid(HumanoidBlock humanoid, ParkingSlot parkingSlot)
    {
        Vector3[] pathPoints = new Vector3[3]
        {
            humanoid.transform.position,
            parkingSlot.HumanoidEntryPoint.position,
            parkingSlot.transform.position
        };

        return pathPoints;
    }

    public Vector3[] GenerateDirectPathToParkingSlot(Transform vehicleTransform, ParkingSlot parkingSlot)
    {
        if (cornerPoints == null)
        {
            return new Vector3[] { parkingSlot.VehicleEntryPoint.position, parkingSlot.transform.position };
        }

        return cornerPoints.GenerateDirectPathToParkingSlot(vehicleTransform, parkingSlot);
    }
    public bool HaveLockedSlot()
    {
        return parkingSlots.Any(slot => slot.IsLocked && !slot.IsVipSlot);
    }
    public void UnlockAlockedSlot()
    {
        parkingSlots.FirstOrDefault(slot => !slot.IsVipSlot && slot.IsLocked).Unlock();
    }
    public ParkingSlot GetVipSlot()
    {
        return parkingSlots[0];
    }
    // public ParkingSlot[] GetAvailableSlots()
    // {
    //     return parkingSlots;
    // }
    private int CountAvailableSlot()
    {
        int count = 0;
        for (int i = 0; i < parkingSlots.Length; i++)
        {
            if (!parkingSlots[i].IsLocked && parkingSlots[i].IsEmpty)
            {
                count++;
            }
        }

        return count;
    }
    public void CheckAvailableSlot()
    {
        if (CountAvailableSlot() == 1)
        {
            WarningLastParkingSlot();
        }
        else
        {
            UnwarningLastParkingSlot();
        }
    }

    public void WarningLastParkingSlot()
    {
        if (Time.time - lastToastTime >= ToastInterval)
        {
            GameManager.Instance.ShowErrorToast("<sprite name=\"icon_warning_toast\"> Only 1 slot left");
            lastToastTime = Time.time;
        }
        warningParkingSlot = parkingSlots.LastOrDefault(slot => !slot.IsLocked && slot.IsEmpty);
        warningParkingSlot.ShowWarningEffect(true);
    }

    public void UnwarningLastParkingSlot()
    {
        if (warningParkingSlot == null) return;

        warningParkingSlot.ShowWarningEffect(false);
        warningParkingSlot = null;
    }

    public bool IsHaveVehicle()
    {
        for (int i = 0; i < parkingSlots.Length; i++)
        {
            if (!parkingSlots[i].IsEmpty)
            {
                return true;
            }
        }
        return false;
    }

    public Vehicle[] GetAllVehicles()
    {
        return parkingSlots.Where(slot => !slot.IsEmpty).Select(slot => slot.ParkedVehicle).ToArray();
    }
}
